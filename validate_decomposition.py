#!/usr/bin/env python3
"""
MEEMD分解结果验证和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import torch
import h5py
import json
from pathlib import Path
import seaborn as sns

def load_processed_data(data_dir="./processed_data"):
    """
    加载处理后的数据
    """
    data_dir = Path(data_dir)
    
    # 加载PyTorch数据
    pt_data = torch.load(data_dir / 'sst_convlstm_data.pt')
    
    # 加载元数据
    with open(data_dir / 'metadata.json', 'r') as f:
        metadata = json.load(f)
    
    return pt_data, metadata

def visualize_imf_components(pt_data, time_idx=0, sample_idx=0, save_dir="./plots"):
    """
    可视化IMF分量
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    sequences = pt_data['sequences']  # (num_samples, seq_len, channels, H, W)
    data_info = pt_data['data_info']
    
    # 获取指定样本和时间步的数据
    sample_data = sequences[sample_idx, time_idx]  # (channels, H, W)
    channel_names = data_info['channel_names']
    
    num_channels = sample_data.shape[0]
    
    # 创建子图
    fig, axes = plt.subplots(2, (num_channels + 1) // 2, figsize=(15, 8))
    axes = axes.flatten() if num_channels > 1 else [axes]
    
    for i in range(num_channels):
        ax = axes[i]
        im = ax.imshow(sample_data[i].numpy(), cmap='RdYlBu_r', aspect='auto')
        ax.set_title(f'{channel_names[i]}')
        ax.set_xlabel('Longitude Index')
        ax.set_ylabel('Latitude Index')
        plt.colorbar(im, ax=ax, shrink=0.8)
    
    # 隐藏多余的子图
    for i in range(num_channels, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig(save_dir / f'imf_components_sample{sample_idx}_time{time_idx}.png', 
                dpi=150, bbox_inches='tight')
    plt.show()

def analyze_imf_statistics(pt_data, save_dir="./plots"):
    """
    分析IMF分量的统计特性
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    sequences = pt_data['sequences']  # (num_samples, seq_len, channels, H, W)
    data_info = pt_data['data_info']
    channel_names = data_info['channel_names']
    
    num_channels = sequences.shape[2]
    
    # 计算每个通道的统计信息
    stats = {}
    for i, channel_name in enumerate(channel_names):
        channel_data = sequences[:, :, i, :, :].numpy()
        stats[channel_name] = {
            'mean': np.mean(channel_data),
            'std': np.std(channel_data),
            'min': np.min(channel_data),
            'max': np.max(channel_data),
            'var': np.var(channel_data)
        }
    
    # 可视化统计信息
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 均值
    means = [stats[name]['mean'] for name in channel_names]
    axes[0, 0].bar(range(len(channel_names)), means)
    axes[0, 0].set_title('Mean Values by Channel')
    axes[0, 0].set_xticks(range(len(channel_names)))
    axes[0, 0].set_xticklabels(channel_names, rotation=45)
    
    # 标准差
    stds = [stats[name]['std'] for name in channel_names]
    axes[0, 1].bar(range(len(channel_names)), stds)
    axes[0, 1].set_title('Standard Deviation by Channel')
    axes[0, 1].set_xticks(range(len(channel_names)))
    axes[0, 1].set_xticklabels(channel_names, rotation=45)
    
    # 范围
    mins = [stats[name]['min'] for name in channel_names]
    maxs = [stats[name]['max'] for name in channel_names]
    x_pos = np.arange(len(channel_names))
    axes[1, 0].bar(x_pos, maxs, label='Max')
    axes[1, 0].bar(x_pos, mins, label='Min')
    axes[1, 0].set_title('Value Range by Channel')
    axes[1, 0].set_xticks(range(len(channel_names)))
    axes[1, 0].set_xticklabels(channel_names, rotation=45)
    axes[1, 0].legend()
    
    # 方差
    vars = [stats[name]['var'] for name in channel_names]
    axes[1, 1].bar(range(len(channel_names)), vars)
    axes[1, 1].set_title('Variance by Channel')
    axes[1, 1].set_xticks(range(len(channel_names)))
    axes[1, 1].set_xticklabels(channel_names, rotation=45)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'imf_statistics.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("IMF分量统计信息:")
    print("-" * 60)
    for name, stat in stats.items():
        print(f"{name:15s}: mean={stat['mean']:8.4f}, std={stat['std']:8.4f}, "
              f"range=[{stat['min']:8.4f}, {stat['max']:8.4f}]")
    
    return stats

def plot_time_series_comparison(pt_data, spatial_point=(50, 70), save_dir="./plots"):
    """
    比较原始SST和IMF分量的时间序列
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    sequences = pt_data['sequences']  # (num_samples, seq_len, channels, H, W)
    data_info = pt_data['data_info']
    channel_names = data_info['channel_names']
    
    # 选择一个空间点和时间序列
    sample_idx = 0
    h_idx, w_idx = spatial_point
    
    # 提取时间序列
    time_series = sequences[sample_idx, :, :, h_idx, w_idx].numpy()  # (seq_len, channels)
    
    plt.figure(figsize=(15, 10))
    
    num_channels = time_series.shape[1]
    for i in range(num_channels):
        plt.subplot(num_channels, 1, i+1)
        plt.plot(time_series[:, i], label=channel_names[i])
        plt.title(f'{channel_names[i]} Time Series at Point ({h_idx}, {w_idx})')
        plt.xlabel('Time Step')
        plt.ylabel('Value')
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    plt.savefig(save_dir / f'time_series_comparison_point_{h_idx}_{w_idx}.png', 
                dpi=150, bbox_inches='tight')
    plt.show()

def validate_reconstruction(pt_data, sample_idx=0, time_idx=0, save_dir="./plots"):
    """
    验证重构质量（原始SST vs IMF分量之和）
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    sequences = pt_data['sequences']  # (num_samples, seq_len, channels, H, W)
    
    # 获取原始SST和IMF分量
    sample_data = sequences[sample_idx, time_idx]  # (channels, H, W)
    original_sst = sample_data[0].numpy()  # 第一个通道是原始SST
    imf_components = sample_data[1:].numpy()  # 其余是IMF分量
    
    # 重构信号
    reconstructed = np.sum(imf_components, axis=0)
    
    # 计算重构误差
    reconstruction_error = original_sst - reconstructed
    mse = np.mean(reconstruction_error**2)
    mae = np.mean(np.abs(reconstruction_error))
    
    # 可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 原始SST
    im1 = axes[0, 0].imshow(original_sst, cmap='RdYlBu_r', aspect='auto')
    axes[0, 0].set_title('Original SST')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
    
    # 重构SST
    im2 = axes[0, 1].imshow(reconstructed, cmap='RdYlBu_r', aspect='auto')
    axes[0, 1].set_title('Reconstructed SST (Sum of IMFs)')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
    
    # 重构误差
    im3 = axes[1, 0].imshow(reconstruction_error, cmap='RdBu', aspect='auto')
    axes[1, 0].set_title(f'Reconstruction Error\nMSE: {mse:.6f}, MAE: {mae:.6f}')
    plt.colorbar(im3, ax=axes[1, 0], shrink=0.8)
    
    # 误差直方图
    axes[1, 1].hist(reconstruction_error.flatten(), bins=50, alpha=0.7)
    axes[1, 1].set_title('Error Distribution')
    axes[1, 1].set_xlabel('Error Value')
    axes[1, 1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig(save_dir / f'reconstruction_validation_sample{sample_idx}_time{time_idx}.png', 
                dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"重构质量评估:")
    print(f"  均方误差 (MSE): {mse:.6f}")
    print(f"  平均绝对误差 (MAE): {mae:.6f}")
    print(f"  相对误差 (%): {100 * mae / np.mean(np.abs(original_sst)):.4f}%")
    
    return mse, mae

def main():
    """
    主验证函数
    """
    print("加载处理后的数据...")
    pt_data, metadata = load_processed_data()
    
    print("数据信息:")
    print(f"  样本数量: {pt_data['sequences'].shape[0]}")
    print(f"  序列长度: {pt_data['sequences'].shape[1]}")
    print(f"  通道数量: {pt_data['sequences'].shape[2]}")
    print(f"  空间尺寸: {pt_data['sequences'].shape[3:5]}")
    
    # 创建图表目录
    Path("./plots").mkdir(exist_ok=True)
    
    print("\n1. 可视化IMF分量...")
    visualize_imf_components(pt_data)
    
    print("\n2. 分析IMF统计特性...")
    stats = analyze_imf_statistics(pt_data)
    
    print("\n3. 绘制时间序列比较...")
    plot_time_series_comparison(pt_data)
    
    print("\n4. 验证重构质量...")
    mse, mae = validate_reconstruction(pt_data)
    
    print("\n验证完成！图表已保存到 ./plots 目录")

if __name__ == "__main__":
    main()
