#!/usr/bin/env python3
"""
MEEMD分解测试脚本
用于测试代码是否能正常运行
"""

import numpy as np
import matplotlib.pyplot as plt
from meemd_decomposition import MEEMD2D

def test_meemd_2d():
    """
    测试二维MEEMD分解
    """
    print("测试二维MEEMD分解...")
    
    # 创建测试数据：简单的二维信号
    H, W = 20, 30
    x = np.linspace(0, 4*np.pi, W)
    y = np.linspace(0, 2*np.pi, H)
    X, Y = np.meshgrid(x, y)
    
    # 创建包含多个频率分量的测试信号
    signal_2d = (2 * np.sin(X) + 
                 1.5 * np.sin(2*X) * np.cos(Y) + 
                 0.8 * np.sin(4*X + Y) + 
                 0.3 * np.random.randn(H, W))
    
    print(f"测试信号形状: {signal_2d.shape}")
    print(f"测试信号范围: {signal_2d.min():.3f} - {signal_2d.max():.3f}")
    
    # 初始化MEEMD分解器（使用较小的参数以加快测试速度）
    meemd = MEEMD2D(trials=10, noise_scale=0.01)
    
    # 执行分解
    print("开始分解...")
    imfs = meemd.decompose_2d(signal_2d)
    
    print(f"分解完成！得到{len(imfs)}个IMF分量")
    
    # 可视化结果
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    # 显示原始信号
    im0 = axes[0].imshow(signal_2d, cmap='RdYlBu_r', aspect='auto')
    axes[0].set_title('Original Signal')
    plt.colorbar(im0, ax=axes[0], shrink=0.8)
    
    # 显示前几个IMF分量
    for i, imf in enumerate(imfs[:min(4, len(imfs))]):
        ax_idx = i + 1
        if ax_idx < len(axes):
            im = axes[ax_idx].imshow(imf, cmap='RdYlBu_r', aspect='auto')
            axes[ax_idx].set_title(f'IMF {i+1}')
            plt.colorbar(im, ax=axes[ax_idx], shrink=0.8)
    
    # 显示重构信号
    if len(imfs) > 0:
        reconstructed = np.sum(imfs, axis=0)
        im_rec = axes[5].imshow(reconstructed, cmap='RdYlBu_r', aspect='auto')
        axes[5].set_title('Reconstructed')
        plt.colorbar(im_rec, ax=axes[5], shrink=0.8)
        
        # 计算重构误差
        error = signal_2d - reconstructed
        mse = np.mean(error**2)
        print(f"重构均方误差: {mse:.6f}")
    
    plt.tight_layout()
    plt.savefig('test_meemd_2d.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return imfs

def test_sst_data_loading():
    """
    测试SST数据加载
    """
    print("\n测试SST数据加载...")
    
    try:
        from sst_data_processor import SSTDataProcessor
        
        processor = SSTDataProcessor('SST-V2.nc')
        sst_data, metadata = processor.load_sst_data()
        
        print(f"SST数据加载成功！")
        print(f"数据形状: {sst_data.shape}")
        print(f"数据范围: {sst_data.min():.2f} - {sst_data.max():.2f} °C")
        print(f"时间范围: {metadata['time_range']}")
        
        return True
        
    except Exception as e:
        print(f"SST数据加载失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("="*60)
    print("MEEMD分解测试")
    print("="*60)
    
    # 测试1: 二维MEEMD分解
    try:
        imfs = test_meemd_2d()
        print("✓ 二维MEEMD分解测试通过")
    except Exception as e:
        print(f"✗ 二维MEEMD分解测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: SST数据加载
    try:
        success = test_sst_data_loading()
        if success:
            print("✓ SST数据加载测试通过")
        else:
            print("✗ SST数据加载测试失败")
    except Exception as e:
        print(f"✗ SST数据加载测试失败: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
