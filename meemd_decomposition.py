#!/usr/bin/env python3
"""
MEEMD (Multivariate Empirical Mode Decomposition) for SST Data
实现二维MEEMD分解，将SST数据分解为多个IMF分量
"""

import numpy as np
import xarray as xr
import torch
from PyEMD import EEMD
import os
from pathlib import Path
import pickle
import h5py
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class MEEMD2D:
    """
    二维MEEMD分解器
    按照论文描述的三步法实现：
    1. 第一维度EEMD分解
    2. 第二维度EEMD分解  
    3. 基于可比最小尺度原则重组IMF
    """
    
    def __init__(self, trials=100, noise_scale=0.005, S_number=4, num_siftings=50):
        """
        初始化MEEMD分解器
        
        Args:
            trials: EEMD集成次数
            noise_scale: 噪声标准差相对于信号标准差的比例
            S_number: 停止准则参数
            num_siftings: 最大筛选次数
        """
        self.trials = trials
        self.noise_scale = noise_scale
        self.S_number = S_number
        self.num_siftings = num_siftings
        
    def _eemd_1d(self, signal):
        """
        对一维信号进行EEMD分解
        
        Args:
            signal: 一维信号数组
            
        Returns:
            imfs: IMF分量列表
        """
        eemd = EEMD(trials=self.trials, noise_scale=self.noise_scale, 
                   S_number=self.S_number, num_siftings=self.num_siftings)
        
        # 处理NaN值
        if np.any(np.isnan(signal)):
            # 简单插值处理NaN
            mask = ~np.isnan(signal)
            if np.sum(mask) < 3:  # 如果有效点太少，返回原信号
                return [signal]
            
            indices = np.arange(len(signal))
            signal_clean = np.interp(indices, indices[mask], signal[mask])
        else:
            signal_clean = signal.copy()
            
        try:
            imfs = eemd.eemd(signal_clean)
            return imfs
        except Exception as e:
            print(f"EEMD分解失败: {e}")
            return [signal_clean]
    
    def decompose_2d(self, image_2d):
        """
        对二维图像进行MEEMD分解
        
        Args:
            image_2d: 二维图像数组 (H, W)
            
        Returns:
            final_imfs: 最终的二维IMF分量列表
        """
        H, W = image_2d.shape
        
        print("步骤1: 沿行方向进行EEMD分解...")
        # 步骤1: 沿第一维度(行)分解
        g_components = []  # 中间分量g_j
        max_imfs_row = 0
        
        # 对每一行进行EEMD分解
        row_imfs_all = []
        for i in tqdm(range(H), desc="行分解"):
            row_signal = image_2d[i, :]
            row_imfs = self._eemd_1d(row_signal)
            row_imfs_all.append(row_imfs)
            max_imfs_row = max(max_imfs_row, len(row_imfs))
        
        # 重组第j个IMF分量为二维图像g_j
        for j in range(max_imfs_row):
            g_j = np.zeros((H, W))
            for i in range(H):
                if j < len(row_imfs_all[i]):
                    g_j[i, :] = row_imfs_all[i][j]
                else:
                    # 如果某行的IMF数量不足，用最后一个IMF填充
                    g_j[i, :] = row_imfs_all[i][-1]
            g_components.append(g_j)
        
        print(f"第一步完成，得到{len(g_components)}个中间分量")
        
        print("步骤2: 沿列方向进行EEMD分解...")
        # 步骤2: 对每个g_j沿第二维度(列)分解
        h_matrix = []  # h_{j,k}矩阵
        
        for j, g_j in enumerate(tqdm(g_components, desc="列分解")):
            h_row = []  # h矩阵的第j行
            max_imfs_col = 0
            
            # 对g_j的每一列进行EEMD分解
            col_imfs_all = []
            for k in range(W):
                col_signal = g_j[:, k]
                col_imfs = self._eemd_1d(col_signal)
                col_imfs_all.append(col_imfs)
                max_imfs_col = max(max_imfs_col, len(col_imfs))
            
            # 重组第k个IMF分量为二维图像h_{j,k}
            for k in range(max_imfs_col):
                h_jk = np.zeros((H, W))
                for col_idx in range(W):
                    if k < len(col_imfs_all[col_idx]):
                        h_jk[:, col_idx] = col_imfs_all[col_idx][k]
                    else:
                        h_jk[:, col_idx] = col_imfs_all[col_idx][-1]
                h_row.append(h_jk)
            
            h_matrix.append(h_row)
        
        print(f"第二步完成，得到{len(h_matrix)}×{len(h_matrix[0]) if h_matrix else 0}的h矩阵")
        
        print("步骤3: 基于可比最小尺度原则重组IMF...")
        # 步骤3: 基于可比最小尺度原则重组
        final_imfs = []
        
        # 确定最终IMF的数量
        max_final_imfs = min(len(h_matrix), max(len(row) for row in h_matrix) if h_matrix else 0)
        
        for i in range(max_final_imfs):
            C_i = np.zeros((H, W))
            
            # 第i个最终IMF由h矩阵的第i行和第i列组成
            # 第i行: h_{i,0}, h_{i,1}, h_{i,2}, ...
            if i < len(h_matrix):
                for k in range(len(h_matrix[i])):
                    if k >= i:  # 只取对角线及以上部分
                        C_i += h_matrix[i][k]
            
            # 第i列: h_{0,i}, h_{1,i}, h_{2,i}, ... (对角线以下部分)
            for j in range(len(h_matrix)):
                if j > i and i < len(h_matrix[j]):
                    C_i += h_matrix[j][i]
            
            final_imfs.append(C_i)
        
        print(f"最终得到{len(final_imfs)}个二维IMF分量")
        return final_imfs
    
    def decompose_3d_sst(self, sst_data, time_indices=None):
        """
        对三维SST数据进行MEEMD分解
        
        Args:
            sst_data: 三维SST数据 (T, H, W)
            time_indices: 要处理的时间索引列表，None表示处理所有时间步
            
        Returns:
            all_imfs: 所有时间步的IMF分量 (T, num_imfs, H, W)
        """
        T, H, W = sst_data.shape
        
        if time_indices is None:
            time_indices = range(T)
        
        # 先对第一个时间步进行分解，确定IMF数量
        print("对第一个时间步进行分解以确定IMF数量...")
        first_imfs = self.decompose_2d(sst_data[time_indices[0]])
        num_imfs = len(first_imfs)
        
        print(f"确定IMF数量: {num_imfs}")
        
        # 初始化结果数组
        all_imfs = np.zeros((len(time_indices), num_imfs, H, W))
        
        # 存储第一个时间步的结果
        for i, imf in enumerate(first_imfs):
            all_imfs[0, i] = imf
        
        # 处理剩余时间步
        for t_idx, t in enumerate(time_indices[1:], 1):
            print(f"处理时间步 {t_idx+1}/{len(time_indices)}")
            imfs = self.decompose_2d(sst_data[t])

            # 确保IMF数量一致
            for i in range(num_imfs):
                if i < len(imfs):
                    all_imfs[t_idx, i] = imfs[i]
                else:
                    # 如果IMF数量不足，用最后一个IMF填充
                    all_imfs[t_idx, i] = imfs[-1] if imfs else np.zeros((H, W))
        
        return all_imfs
