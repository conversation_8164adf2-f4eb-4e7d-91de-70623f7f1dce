# SST数据MEEMD分解与ConvLSTM预处理

本项目实现了海表温度(SST)数据的二维多元经验模态分解(MEEMD)，并将分解后的IMF分量保存为适合PyTorch ConvLSTM模型训练的格式。

## 项目结构

```
MEEMD_code/
├── meemd_decomposition.py      # MEEMD分解核心算法
├── sst_data_processor.py       # SST数据处理器
├── main_process_sst.py         # 主执行脚本
├── validate_decomposition.py   # 分解结果验证和可视化
├── test_meemd.py              # 基础测试脚本
├── test_with_simulated_data.py # 模拟数据测试脚本
├── data_inspection.py         # 数据检查脚本
├── SST-V2.nc                  # SST数据文件
└── README.md                  # 说明文档
```

## 核心功能

### 1. MEEMD分解算法 (`meemd_decomposition.py`)

实现了论文中描述的三步MEEMD分解方法：

- **步骤1**: 沿第一维度(行)进行EEMD分解
- **步骤2**: 沿第二维度(列)对中间分量进行EEMD分解
- **步骤3**: 基于"可比最小尺度原则"重组IMF分量

主要类：
- `MEEMD2D`: 二维MEEMD分解器
- `decompose_2d()`: 对单个二维图像进行分解
- `decompose_3d_sst()`: 对三维SST时间序列数据进行分解

### 2. 数据处理器 (`sst_data_processor.py`)

负责SST数据的加载、预处理和保存：

- 支持NetCDF格式的SST数据读取
- 自动单位转换(开尔文→摄氏度)
- 生成适合ConvLSTM训练的序列数据
- 支持多种数据格式输出(.pt, .h5, .json)

主要类：
- `SSTDataProcessor`: SST数据处理器
- `load_sst_data()`: 加载SST数据
- `perform_meemd_decomposition()`: 执行MEEMD分解
- `save_for_pytorch()`: 保存为PyTorch格式

## 使用方法

### 1. 快速开始

使用模拟数据测试完整流程：

```bash
python test_with_simulated_data.py
```

### 2. 处理真实SST数据

```bash
# 处理少量数据进行测试
python main_process_sst.py --sample_size 100

# 处理全部数据
python main_process_sst.py --full_data

# 自定义参数
python main_process_sst.py --sst_file SST-V2.nc --output_dir ./results --sequence_length 10 --prediction_length 1
```

### 3. 数据检查

检查SST数据的基本信息：

```bash
python data_inspection.py
```

### 4. 结果验证

验证和可视化分解结果：

```bash
python validate_decomposition.py
```

## 输出数据格式

处理后的数据保存为以下格式，适合PyTorch ConvLSTM训练：

### PyTorch格式 (.pt文件)
```python
{
    'sequences': torch.Tensor,      # 输入序列 (N, seq_len, channels, H, W)
    'targets': torch.Tensor,        # 目标数据 (N, pred_len, H, W)
    'metadata': dict,               # 原始数据元信息
    'decomp_metadata': dict,        # 分解参数和信息
    'data_info': {
        'num_samples': int,         # 样本数量
        'sequence_length': int,     # 序列长度
        'prediction_length': int,   # 预测长度
        'num_channels': int,        # 通道数 (原始SST + IMF分量)
        'spatial_shape': tuple,     # 空间尺寸 (H, W)
        'channel_names': list       # 通道名称列表
    }
}
```

### 数据维度说明
- `sequences`: (N, seq_len, channels, H, W)
  - N: 样本数量
  - seq_len: 输入序列长度
  - channels: 通道数 = 1(原始SST) + num_imfs(IMF分量数)
  - H, W: 空间维度(纬度×经度)

- `targets`: (N, pred_len, H, W)
  - pred_len: 预测长度

## 参数配置

### MEEMD分解参数
- `trials`: EEMD集成次数 (默认: 50)
- `noise_scale`: 噪声标准差比例 (默认: 0.005)
- `S_number`: 停止准则参数 (默认: 4)
- `num_siftings`: 最大筛选次数 (默认: 50)

### 数据处理参数
- `sequence_length`: 输入序列长度 (默认: 10)
- `prediction_length`: 预测长度 (默认: 1)
- `sample_size`: 处理的时间步数量 (用于测试)

## 依赖库

- numpy
- torch
- PyEMD
- matplotlib
- tqdm
- h5py
- xarray (可选，用于NetCDF读取)
- netCDF4 (可选，备用NetCDF读取)

## 测试结果

使用模拟数据测试显示：
- 成功分解出4个IMF分量
- 生成的训练数据格式正确
- 数据维度: (10, 5, 5, 20, 25) - 10个样本，5个时间步，5个通道，20×25空间网格

## 注意事项

1. **内存使用**: MEEMD分解是计算密集型操作，建议先用小样本测试
2. **处理时间**: 每个时间步的分解需要几分钟，建议合理设置sample_size
3. **数据质量**: 确保SST数据没有过多缺失值
4. **参数调优**: 根据数据特性调整MEEMD参数以获得最佳分解效果

## 后续使用

生成的数据可以直接用于PyTorch ConvLSTM模型训练：

```python
import torch

# 加载数据
data = torch.load('processed_data/sst_convlstm_data.pt')
sequences = data['sequences']  # 输入序列
targets = data['targets']      # 目标数据

# 创建数据加载器
from torch.utils.data import DataLoader, TensorDataset
dataset = TensorDataset(sequences, targets)
dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

# 用于ConvLSTM训练
for batch_sequences, batch_targets in dataloader:
    # batch_sequences: (batch_size, seq_len, channels, H, W)
    # batch_targets: (batch_size, pred_len, H, W)
    pass
```
