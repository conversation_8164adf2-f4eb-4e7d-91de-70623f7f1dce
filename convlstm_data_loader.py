#!/usr/bin/env python3
"""
ConvLSTM数据加载器示例
展示如何使用MEEMD分解后的SST数据进行ConvLSTM模型训练
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset, random_split
import json
import numpy as np
from pathlib import Path

class SSTDataset:
    """
    SST数据集类，用于加载和管理MEEMD分解后的数据
    """
    
    def __init__(self, data_path="./processed_data/simulated_sst_convlstm_data.pt"):
        """
        初始化数据集
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.metadata = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        print(f"加载数据: {self.data_path}")
        
        if not Path(self.data_path).exists():
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")
        
        self.data = torch.load(self.data_path)
        self.metadata = self.data.get('metadata', {})
        
        print("数据加载完成!")
        self.print_data_info()
    
    def print_data_info(self):
        """打印数据信息"""
        data_info = self.data['data_info']
        
        print("\n数据集信息:")
        print(f"  样本数量: {data_info['num_samples']}")
        print(f"  序列长度: {data_info['sequence_length']}")
        print(f"  预测长度: {data_info['prediction_length']}")
        print(f"  通道数量: {data_info['num_channels']}")
        print(f"  空间尺寸: {data_info['spatial_shape']}")
        print(f"  通道名称: {data_info['channel_names']}")
        
        sequences = self.data['sequences']
        targets = self.data['targets']
        print(f"\n张量形状:")
        print(f"  输入序列: {sequences.shape}")
        print(f"  目标数据: {targets.shape}")
        
        print(f"\n数据范围:")
        print(f"  输入数据: {sequences.min():.4f} ~ {sequences.max():.4f}")
        print(f"  目标数据: {targets.min():.4f} ~ {targets.max():.4f}")
    
    def get_dataloaders(self, batch_size=4, train_ratio=0.8, shuffle=True):
        """
        创建训练和验证数据加载器
        
        Args:
            batch_size: 批次大小
            train_ratio: 训练集比例
            shuffle: 是否打乱数据
            
        Returns:
            train_loader, val_loader: 训练和验证数据加载器
        """
        sequences = self.data['sequences']
        targets = self.data['targets']
        
        # 创建数据集
        dataset = TensorDataset(sequences, targets)
        
        # 划分训练集和验证集
        total_size = len(dataset)
        train_size = int(train_ratio * total_size)
        val_size = total_size - train_size
        
        train_dataset, val_dataset = random_split(
            dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, 
            shuffle=shuffle, drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, 
            shuffle=False, drop_last=True
        )
        
        print(f"\n数据加载器创建完成:")
        print(f"  训练集大小: {len(train_dataset)}")
        print(f"  验证集大小: {len(val_dataset)}")
        print(f"  批次大小: {batch_size}")
        
        return train_loader, val_loader
    
    def get_channel_statistics(self):
        """
        计算各通道的统计信息
        
        Returns:
            stats: 各通道统计信息字典
        """
        sequences = self.data['sequences']  # (N, seq_len, channels, H, W)
        channel_names = self.data['data_info']['channel_names']
        
        stats = {}
        for i, channel_name in enumerate(channel_names):
            channel_data = sequences[:, :, i, :, :].numpy()
            stats[channel_name] = {
                'mean': np.mean(channel_data),
                'std': np.std(channel_data),
                'min': np.min(channel_data),
                'max': np.max(channel_data)
            }
        
        return stats

class SimpleConvLSTM(nn.Module):
    """
    简单的ConvLSTM模型示例
    """
    
    def __init__(self, input_channels, hidden_channels, kernel_size, num_layers=1):
        """
        初始化ConvLSTM模型
        
        Args:
            input_channels: 输入通道数
            hidden_channels: 隐藏层通道数
            kernel_size: 卷积核大小
            num_layers: LSTM层数
        """
        super(SimpleConvLSTM, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        
        # 简化的ConvLSTM实现（这里用Conv2D + LSTM的组合代替）
        self.conv_input = nn.Conv2d(input_channels, hidden_channels, kernel_size, padding=kernel_size//2)
        self.conv_hidden = nn.Conv2d(hidden_channels, hidden_channels, kernel_size, padding=kernel_size//2)
        
        # 输出层
        self.conv_output = nn.Conv2d(hidden_channels, 1, 1)  # 输出单通道（SST预测）
        
        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch_size, seq_len, channels, H, W)
            
        Returns:
            output: 预测结果 (batch_size, 1, H, W)
        """
        batch_size, seq_len, channels, H, W = x.shape
        
        # 简化处理：只使用最后一个时间步的数据
        x_last = x[:, -1, :, :, :]  # (batch_size, channels, H, W)
        
        # 卷积处理
        hidden = self.relu(self.conv_input(x_last))
        hidden = self.relu(self.conv_hidden(hidden))
        
        # 输出预测
        output = self.conv_output(hidden)  # (batch_size, 1, H, W)
        
        return output

def demonstrate_training_loop():
    """
    演示训练循环
    """
    print("="*60)
    print("ConvLSTM训练示例")
    print("="*60)
    
    # 1. 加载数据
    dataset = SSTDataset()
    train_loader, val_loader = dataset.get_dataloaders(batch_size=2)
    
    # 2. 创建模型
    data_info = dataset.data['data_info']
    input_channels = data_info['num_channels']
    
    model = SimpleConvLSTM(
        input_channels=input_channels,
        hidden_channels=32,
        kernel_size=3
    )
    
    print(f"\n模型信息:")
    print(f"  输入通道数: {input_channels}")
    print(f"  模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 3. 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 4. 训练几个epoch（演示）
    model.train()
    print(f"\n开始训练演示...")
    
    for epoch in range(2):  # 只训练2个epoch作为演示
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (sequences, targets) in enumerate(train_loader):
            # sequences: (batch_size, seq_len, channels, H, W)
            # targets: (batch_size, pred_len, H, W)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(sequences)  # (batch_size, 1, H, W)
            
            # 计算损失
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx == 0:  # 只打印第一个batch的信息
                print(f"  Epoch {epoch+1}, Batch {batch_idx+1}:")
                print(f"    输入形状: {sequences.shape}")
                print(f"    目标形状: {targets.shape}")
                print(f"    输出形状: {outputs.shape}")
                print(f"    损失: {loss.item():.6f}")
        
        avg_loss = total_loss / num_batches
        print(f"  Epoch {epoch+1} 平均损失: {avg_loss:.6f}")
    
    print("\n训练演示完成!")

def analyze_channel_importance():
    """
    分析各通道的重要性
    """
    print("\n" + "="*60)
    print("通道重要性分析")
    print("="*60)
    
    dataset = SSTDataset()
    stats = dataset.get_channel_statistics()
    
    print("\n各通道统计信息:")
    print("-" * 60)
    for channel_name, stat in stats.items():
        print(f"{channel_name:15s}: "
              f"mean={stat['mean']:8.4f}, "
              f"std={stat['std']:8.4f}, "
              f"range=[{stat['min']:8.4f}, {stat['max']:8.4f}]")
    
    # 计算各通道的变异系数（标准差/均值）
    print("\n通道变异系数 (CV = std/|mean|):")
    print("-" * 40)
    cv_values = []
    for channel_name, stat in stats.items():
        cv = stat['std'] / abs(stat['mean']) if stat['mean'] != 0 else float('inf')
        cv_values.append((channel_name, cv))
        print(f"{channel_name:15s}: {cv:.4f}")
    
    # 按变异系数排序
    cv_values.sort(key=lambda x: x[1], reverse=True)
    print(f"\n按变异性排序的通道:")
    for i, (channel_name, cv) in enumerate(cv_values):
        print(f"{i+1}. {channel_name}: {cv:.4f}")

def main():
    """
    主函数
    """
    try:
        # 演示数据加载和训练
        demonstrate_training_loop()
        
        # 分析通道重要性
        analyze_channel_importance()
        
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请先运行 test_with_simulated_data.py 生成测试数据")
    except Exception as e:
        print(f"运行时错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
