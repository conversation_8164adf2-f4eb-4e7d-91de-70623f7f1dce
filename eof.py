"""
创新的EOF-ConvLSTM混合预测模型
结合EOF空间模态和ConvLSTM时空建模的高精度SST预测系统
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, List, Optional

class SpatialAttention(nn.Module):
    """空间注意力机制 - 增强对关键空间区域的关注"""
    def __init__(self, in_channels: int):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, in_channels // 8, 1)
        self.conv2 = nn.Conv2d(in_channels // 8, 1, 1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # x: [B, C, H, W]
        attention = self.conv1(x)
        attention = F.relu(attention)
        attention = self.conv2(attention)
        attention = self.sigmoid(attention)
        return x * attention

class TemporalAttention(nn.Module):
    """时间注意力机制 - 增强对关键时间步的关注"""
    def __init__(self, hidden_dim: int):
        super(TemporalAttention, self).__init__()
        self.attention_layer = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        # x: [B, T, H, W, C]
        B, T, H, W, C = x.shape
        x_flat = x.view(B, T, -1)  # [B, T, H*W*C]

        # 计算每个时间步的注意力权重
        attention_weights = []
        for t in range(T):
            weight = self.attention_layer(x_flat[:, t, :])  # [B, 1]
            attention_weights.append(weight)

        attention_weights = torch.stack(attention_weights, dim=1)  # [B, T, 1]
        attention_weights = F.softmax(attention_weights, dim=1)

        # 应用注意力权重
        weighted_x = x * attention_weights.unsqueeze(-1).unsqueeze(-1)
        return weighted_x

class EOFEmbedding(nn.Module):
    """EOF模态嵌入层 - 将EOF空间模态信息融入网络"""
    def __init__(self, eof_modes: torch.Tensor, embed_dim: int = 64):
        super(EOFEmbedding, self).__init__()
        self.eof_modes = nn.Parameter(eof_modes, requires_grad=False)  # 固定EOF模态
        self.spatial_points, self.n_modes = eof_modes.shape

        # EOF模态特征提取
        self.eof_encoder = nn.Sequential(
            nn.Linear(self.n_modes, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU()
        )

        # 空间位置编码
        self.position_encoder = nn.Sequential(
            nn.Linear(2, embed_dim // 2),  # 经纬度位置
            nn.ReLU(),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )

        self.fusion = nn.Linear(embed_dim + embed_dim // 2, embed_dim)

    def forward(self, spatial_coords: torch.Tensor) -> torch.Tensor:
        """
        Args:
            spatial_coords: [H*W, 2] 空间坐标(经度, 纬度)
        Returns:
            eof_features: [H*W, embed_dim] EOF嵌入特征
        """
        H_W = spatial_coords.shape[0]  # 实际空间点数 (H*W)

        # EOF模态特征 - 需要重塑为每个变量的空间点
        eof_features_full = self.eof_encoder(self.eof_modes)  # [28000, embed_dim]

        # 分离SST和t2m的EOF特征，只使用SST部分（前14000个点）
        spatial_points_per_var = self.spatial_points // 2
        eof_features_sst = eof_features_full[:spatial_points_per_var, :]  # [14000, embed_dim]

        # 确保维度匹配
        if eof_features_sst.shape[0] != H_W:
            # 如果维度不匹配，进行插值或截取
            if eof_features_sst.shape[0] > H_W:
                eof_features_sst = eof_features_sst[:H_W, :]
            else:
                # 重复最后的特征以匹配维度
                repeat_times = H_W // eof_features_sst.shape[0] + 1
                eof_features_sst = eof_features_sst.repeat(repeat_times, 1)[:H_W, :]

        # 位置编码
        pos_features = self.position_encoder(spatial_coords)  # [H*W, embed_dim//2]

        # 特征融合
        combined = torch.cat([eof_features_sst, pos_features], dim=-1)
        fused_features = self.fusion(combined)

        return fused_features

class MultiVariableCoupling(nn.Module):
    """多变量耦合模块 - 建模SST和t2m之间的相互作用（内存优化版）"""
    def __init__(self, channels: int):
        super(MultiVariableCoupling, self).__init__()
        self.sst_branch = nn.Conv2d(1, channels // 2, 3, padding=1)
        self.t2m_branch = nn.Conv2d(1, channels // 2, 3, padding=1)

        # 简化的交叉注意力 - 减少内存使用
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 4, channels, 1),
            nn.Sigmoid()
        )

        # 融合层
        self.fusion_conv = nn.Conv2d(channels, channels, 3, padding=1)
        self.norm = nn.BatchNorm2d(channels)

    def forward(self, sst: torch.Tensor, t2m: torch.Tensor) -> torch.Tensor:
        """
        Args:
            sst: [B, 1, H, W] SST数据
            t2m: [B, 1, H, W] t2m数据
        Returns:
            coupled_features: [B, channels, H, W] 耦合特征
        """
        # 分支特征提取
        sst_feat = self.sst_branch(sst)  # [B, channels//2, H, W]
        t2m_feat = self.t2m_branch(t2m)  # [B, channels//2, H, W]

        # 拼接特征
        combined = torch.cat([sst_feat, t2m_feat], dim=1)  # [B, channels, H, W]

        # 通道注意力（内存友好的替代方案）
        attention_weights = self.channel_attention(combined)  # [B, channels, 1, 1]
        attended = combined * attention_weights  # [B, channels, H, W]

        # 融合和归一化
        output = self.fusion_conv(attended)
        output = self.norm(output)
        output = F.relu(output)

        return output

class ConvLSTMCell(nn.Module):
    """ConvLSTM单元 - 核心时空建模组件"""
    def __init__(self, input_dim: int, hidden_dim: int, kernel_size: int = 3):
        super(ConvLSTMCell, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2

        # 输入门、遗忘门、输出门和候选值的卷积层
        self.conv = nn.Conv2d(
            in_channels=input_dim + hidden_dim,
            out_channels=4 * hidden_dim,
            kernel_size=kernel_size,
            padding=self.padding,
            bias=True
        )

    def forward(self, input_tensor: torch.Tensor, cur_state: Tuple[torch.Tensor, torch.Tensor]):
        h_cur, c_cur = cur_state

        # 拼接输入和隐藏状态
        combined = torch.cat([input_tensor, h_cur], dim=1)
        combined_conv = self.conv(combined)

        # 分离四个门
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)

        # 计算门控值
        i = torch.sigmoid(cc_i)  # 输入门
        f = torch.sigmoid(cc_f)  # 遗忘门
        o = torch.sigmoid(cc_o)  # 输出门
        g = torch.tanh(cc_g)     # 候选值

        # 更新细胞状态和隐藏状态
        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)

        return h_next, c_next

    def init_hidden(self, batch_size: int, image_size: Tuple[int, int], device: torch.device):
        height, width = image_size
        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=device),
                torch.zeros(batch_size, self.hidden_dim, height, width, device=device))

class MultiScaleConvLSTM(nn.Module):
    """多尺度ConvLSTM - 捕获不同尺度的时空特征"""
    def __init__(self, input_dim: int, hidden_dims: List[int], kernel_sizes: List[int],
                 num_layers: int, batch_first: bool = True):
        super(MultiScaleConvLSTM, self).__init__()

        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.kernel_sizes = kernel_sizes
        self.num_layers = num_layers
        self.batch_first = batch_first

        # 构建多层ConvLSTM
        cell_list = []
        for i in range(num_layers):
            cur_input_dim = input_dim if i == 0 else hidden_dims[i-1]
            cell_list.append(ConvLSTMCell(
                input_dim=cur_input_dim,
                hidden_dim=hidden_dims[i],
                kernel_size=kernel_sizes[i]
            ))

        self.cell_list = nn.ModuleList(cell_list)

        # 多尺度池化
        self.multi_scale_pools = nn.ModuleList([
            nn.AdaptiveAvgPool2d((32, 32)),  # 粗尺度
            nn.AdaptiveAvgPool2d((64, 64)),  # 中尺度
            nn.Identity()                     # 原尺度
        ])

        # 尺度融合
        self.scale_fusion = nn.Conv2d(
            hidden_dims[-1] * 3, hidden_dims[-1], 1
        )

    def forward(self, input_tensor: torch.Tensor, hidden_state: Optional[List] = None):
        """
        Args:
            input_tensor: [B, T, C, H, W] 或 [T, B, C, H, W]
            hidden_state: 初始隐藏状态
        Returns:
            layer_output_list: 每层的输出
            last_state_list: 最终状态
        """
        if not self.batch_first:
            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)

        b, seq_len, _, h, w = input_tensor.size()

        # 初始化隐藏状态
        if hidden_state is None:
            hidden_state = self._init_hidden(batch_size=b, image_size=(h, w),
                                           device=input_tensor.device)

        layer_output_list = []
        last_state_list = []

        cur_layer_input = input_tensor

        for layer_idx in range(self.num_layers):
            h, c = hidden_state[layer_idx]
            output_inner = []

            for t in range(seq_len):
                h, c = self.cell_list[layer_idx](cur_layer_input[:, t, :, :, :], (h, c))
                output_inner.append(h)

            layer_output = torch.stack(output_inner, dim=1)
            cur_layer_input = layer_output

            layer_output_list.append(layer_output)
            last_state_list.append((h, c))

        # 多尺度特征融合
        final_output = layer_output_list[-1][:, -1, :, :, :]  # 取最后时间步

        # 获取原始尺寸（从tensor形状中获取）
        original_h, original_w = final_output.shape[-2], final_output.shape[-1]

        # 应用多尺度池化
        scale_features = []
        for pool in self.multi_scale_pools:
            pooled = pool(final_output)
            # 上采样回原尺寸
            if pooled.shape[-2:] != (original_h, original_w):
                upsampled = F.interpolate(pooled, size=(original_h, original_w), mode='bilinear', align_corners=False)
            else:
                upsampled = pooled
            scale_features.append(upsampled)

        # 融合多尺度特征
        multi_scale_output = torch.cat(scale_features, dim=1)
        fused_output = self.scale_fusion(multi_scale_output)

        return layer_output_list, last_state_list, fused_output

    def _init_hidden(self, batch_size: int, image_size: Tuple[int, int], device: torch.device):
        init_states = []
        for i in range(self.num_layers):
            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size, device))
        return init_states

class EOFConvLSTMPredictor(nn.Module):
    """
    创新的EOF-ConvLSTM混合预测模型

    核心创新点:
    1. EOF空间模态嵌入 - 利用预训练的EOF模态作为空间先验
    2. 多变量耦合机制 - 建模SST和t2m的物理耦合关系
    3. 多尺度时空注意力 - 自适应关注关键时空特征
    4. 残差连接和跳跃连接 - 增强梯度流动和特征传播
    """

    def __init__(self,
                 eof_modes: torch.Tensor,
                 input_channels: int = 2,  # SST + t2m
                 hidden_dims: List[int] = [64, 128, 64],
                 kernel_sizes: List[int] = [3, 3, 3],
                 num_layers: int = 3,
                 seq_len: int = 10,
                 pred_len: int = 1,
                 embed_dim: int = 64,
                 dropout: float = 0.1):

        super(EOFConvLSTMPredictor, self).__init__()

        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dims = hidden_dims

        # 1. EOF嵌入层
        self.eof_embedding = EOFEmbedding(eof_modes, embed_dim)

        # 2. 多变量耦合层
        self.coupling_layer = MultiVariableCoupling(hidden_dims[0])

        # 3. 输入投影层
        self.input_projection = nn.Conv2d(
            input_channels + embed_dim, hidden_dims[0], 3, padding=1
        )

        # 4. 多尺度ConvLSTM核心
        self.convlstm = MultiScaleConvLSTM(
            input_dim=hidden_dims[0],
            hidden_dims=hidden_dims,
            kernel_sizes=kernel_sizes,
            num_layers=num_layers
        )

        # 5. 时空注意力机制
        self.spatial_attention = SpatialAttention(hidden_dims[-1])
        self.temporal_attention = TemporalAttention(hidden_dims[-1])

        # 6. 特征融合和输出层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(hidden_dims[-1] * 2, hidden_dims[-1], 3, padding=1),
            nn.BatchNorm2d(hidden_dims[-1]),
            nn.ReLU(),
            nn.Dropout2d(dropout)
        )

        # 7. 预测头 - 多步预测
        self.prediction_head = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(hidden_dims[-1], hidden_dims[-1] // 2, 3, padding=1),
                nn.ReLU(),
                nn.Conv2d(hidden_dims[-1] // 2, 1, 1),  # 输出SST
            ) for _ in range(pred_len)
        ])

        # 8. 残差连接
        self.residual_projection = nn.Conv2d(input_channels, 1, 1)

        # 9. 输出归一化
        self.output_norm = nn.BatchNorm2d(1)

    def create_spatial_coordinates(self, height: int, width: int, device: torch.device) -> torch.Tensor:
        """创建空间坐标网格"""
        # 假设经度范围 [0, 360], 纬度范围 [-90, 90]
        lon = torch.linspace(0, 360, width, device=device)
        lat = torch.linspace(-90, 90, height, device=device)

        lon_grid, lat_grid = torch.meshgrid(lon, lat, indexing='xy')
        coords = torch.stack([lon_grid.flatten(), lat_grid.flatten()], dim=1)

        return coords

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [B, T, C, H, W] 输入序列，C=2 (SST, t2m)
        Returns:
            predictions: [B, pred_len, 1, H, W] SST预测结果
        """
        B, T, C, H, W = x.shape
        device = x.device

        # 1. 创建空间坐标并获取EOF嵌入
        spatial_coords = self.create_spatial_coordinates(H, W, device)
        eof_features = self.eof_embedding(spatial_coords)  # [H*W, embed_dim]
        eof_features = eof_features.view(H, W, -1).permute(2, 0, 1)  # [embed_dim, H, W]
        eof_features = eof_features.unsqueeze(0).expand(B, -1, -1, -1)  # [B, embed_dim, H, W]

        # 2. 处理每个时间步
        processed_sequence = []
        for t in range(T):
            # 分离SST和t2m
            sst_t = x[:, t, 0:1, :, :]  # [B, 1, H, W]
            t2m_t = x[:, t, 1:2, :, :]  # [B, 1, H, W]

            # 多变量耦合
            coupled_features = self.coupling_layer(sst_t, t2m_t)  # [B, hidden_dims[0], H, W]

            # 融合EOF嵌入
            combined_features = torch.cat([
                x[:, t, :, :, :],  # 原始输入
                eof_features       # EOF嵌入
            ], dim=1)

            # 输入投影
            projected = self.input_projection(combined_features)

            # 与耦合特征相加（残差连接）
            enhanced_features = projected + coupled_features

            processed_sequence.append(enhanced_features)

        # 3. 重组为序列格式
        sequence_input = torch.stack(processed_sequence, dim=1)  # [B, T, hidden_dims[0], H, W]

        # 4. ConvLSTM时空建模
        layer_outputs, final_states, multi_scale_output = self.convlstm(sequence_input)

        # 5. 应用空间注意力
        spatial_attended = self.spatial_attention(multi_scale_output)

        # 6. 特征融合
        fused_features = torch.cat([multi_scale_output, spatial_attended], dim=1)
        fused_features = self.feature_fusion(fused_features)

        # 7. 多步预测
        predictions = []
        current_features = fused_features

        for step in range(self.pred_len):
            # 预测当前步
            pred_step = self.prediction_head[step](current_features)

            # 残差连接 - 添加输入的SST作为基线
            residual = self.residual_projection(x[:, -1, :, :, :])  # 使用最后一个时间步
            pred_step = pred_step + residual

            # 归一化输出
            pred_step = self.output_norm(pred_step)

            predictions.append(pred_step)

            # 为下一步预测更新特征（可选：使用预测结果更新特征）
            if step < self.pred_len - 1:
                # 这里可以添加递归预测逻辑
                pass

        # 8. 组合预测结果
        predictions = torch.stack(predictions, dim=1)  # [B, pred_len, 1, H, W]

        return predictions

    def get_model_info(self) -> dict:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # 假设float32
            'architecture': 'EOF-ConvLSTM Hybrid',
            'seq_len': self.seq_len,
            'pred_len': self.pred_len,
            'hidden_dims': self.hidden_dims
        }