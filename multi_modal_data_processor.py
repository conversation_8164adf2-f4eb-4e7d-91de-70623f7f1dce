#!/usr/bin/env python3
"""
多模态数据处理器
整合SST、IMF分量和ERA5大气数据，构建ConvLSTM训练数据集
"""

import numpy as np
import torch
import h5py
import json
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
from scipy.interpolate import griddata
from tqdm import tqdm

class MultiModalDataProcessor:
    """
    多模态数据处理器，整合SST+IMF和ERA5数据
    """
    
    def __init__(self, sst_data_path="./processed_data/sst_convlstm_data.pt", 
                 era5_file="data_ERA5.nc", output_dir="./multi_modal_data"):
        """
        初始化多模态数据处理器
        
        Args:
            sst_data_path: SST+IMF数据路径
            era5_file: ERA5数据文件路径
            output_dir: 输出目录
        """
        self.sst_data_path = sst_data_path
        self.era5_file = era5_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def load_sst_imf_data(self):
        """
        加载SST+IMF数据
        
        Returns:
            sst_data: SST+IMF数据
            metadata: 元数据
        """
        print("加载SST+IMF数据...")
        
        if not Path(self.sst_data_path).exists():
            raise FileNotFoundError(f"SST数据文件不存在: {self.sst_data_path}")
        
        data = torch.load(self.sst_data_path, weights_only=False)
        
        print(f"SST+IMF数据形状: {data['sequences'].shape}")
        print(f"通道数: {data['data_info']['num_channels']}")
        print(f"通道名称: {data['data_info']['channel_names']}")
        
        return data
    
    def load_era5_data(self):
        """
        加载ERA5大气数据
        
        Returns:
            era5_data: ERA5数据字典
            era5_coords: 坐标信息
        """
        print("加载ERA5数据...")
        
        try:
            import h5py
            with h5py.File(self.era5_file, 'r') as f:
                era5_data = {
                    'u10': f['u10'][:],      # 10米风速u分量
                    'v10': f['v10'][:],      # 10米风速v分量
                    't2m': f['t2m'][:],      # 2米温度
                    'msl': f['msl'][:]       # 海平面气压
                }
                
                era5_coords = {
                    'time': f['valid_time'][:],
                    'lat': f['latitude'][:],
                    'lon': f['longitude'][:]
                }
                
        except Exception as e:
            print(f"h5py读取失败，尝试其他方法: {e}")
            # 备用方案
            try:
                import xarray as xr
                ds = xr.open_dataset(self.era5_file)
                era5_data = {
                    'u10': ds['u10'].values,
                    'v10': ds['v10'].values,
                    't2m': ds['t2m'].values,
                    'msl': ds['msl'].values
                }
                era5_coords = {
                    'time': ds['valid_time'].values,
                    'lat': ds['latitude'].values,
                    'lon': ds['longitude'].values
                }
                ds.close()
            except Exception as e2:
                raise RuntimeError(f"无法读取ERA5数据: {e2}")
        
        print(f"ERA5数据形状:")
        for var, data in era5_data.items():
            print(f"  {var}: {data.shape}")
        
        print(f"ERA5坐标范围:")
        print(f"  时间: {len(era5_coords['time'])}步")
        print(f"  纬度: {era5_coords['lat'].min():.2f} - {era5_coords['lat'].max():.2f}")
        print(f"  经度: {era5_coords['lon'].min():.2f} - {era5_coords['lon'].max():.2f}")
        
        return era5_data, era5_coords
    
    def interpolate_era5_to_sst_grid(self, era5_data, era5_coords, sst_coords):
        """
        将ERA5数据插值到SST网格
        
        Args:
            era5_data: ERA5数据
            era5_coords: ERA5坐标
            sst_coords: SST坐标
            
        Returns:
            interpolated_data: 插值后的ERA5数据
        """
        print("将ERA5数据插值到SST网格...")
        
        # SST网格信息（从之前的数据检查得到）
        sst_lat = np.linspace(17.025, 21.975, 100)  # SST纬度网格
        sst_lon = np.linspace(113.025, 119.975, 140)  # SST经度网格
        
        # 创建网格
        era5_lon_grid, era5_lat_grid = np.meshgrid(era5_coords['lon'], era5_coords['lat'])
        sst_lon_grid, sst_lat_grid = np.meshgrid(sst_lon, sst_lat)
        
        # 展平坐标用于插值
        era5_points = np.column_stack([era5_lon_grid.ravel(), era5_lat_grid.ravel()])
        sst_points = np.column_stack([sst_lon_grid.ravel(), sst_lat_grid.ravel()])
        
        interpolated_data = {}
        
        for var_name, var_data in era5_data.items():
            print(f"  插值变量: {var_name}")
            T, H_era5, W_era5 = var_data.shape
            
            # 只处理前100个时间步（与SST数据匹配）
            T_process = min(T, 100)
            interpolated_var = np.zeros((T_process, 100, 140))
            
            for t in tqdm(range(T_process), desc=f"插值{var_name}"):
                # 当前时间步的数据
                data_2d = var_data[t]
                
                # 插值到SST网格
                interpolated_2d = griddata(
                    era5_points, data_2d.ravel(), sst_points, 
                    method='linear', fill_value=np.nan
                ).reshape(100, 140)
                
                # 处理NaN值
                if np.any(np.isnan(interpolated_2d)):
                    # 使用最近邻插值填充NaN
                    mask = ~np.isnan(interpolated_2d)
                    if np.any(mask):
                        interpolated_2d = griddata(
                            sst_points[mask.ravel()], interpolated_2d.ravel()[mask.ravel()],
                            sst_points, method='nearest'
                        ).reshape(100, 140)
                
                interpolated_var[t] = interpolated_2d
            
            interpolated_data[var_name] = interpolated_var
        
        print("ERA5数据插值完成!")
        return interpolated_data
    
    def create_multi_modal_dataset(self, sequence_length=14, prediction_length=1):
        """
        创建多模态数据集
        
        Args:
            sequence_length: 输入序列长度
            prediction_length: 预测长度
            
        Returns:
            dataset: 多模态数据集
        """
        print("创建多模态数据集...")
        
        # 1. 加载SST+IMF数据
        sst_data = self.load_sst_imf_data()
        sst_sequences = sst_data['sequences']  # (N, seq_len, sst_channels, H, W)
        sst_targets = sst_data['targets']      # (N, pred_len, H, W)
        
        # 2. 加载ERA5数据
        era5_data, era5_coords = self.load_era5_data()
        
        # 3. 插值ERA5数据到SST网格
        sst_coords = None  # 将使用默认的SST坐标
        interpolated_era5 = self.interpolate_era5_to_sst_grid(era5_data, era5_coords, sst_coords)
        
        # 4. 构建多模态序列
        print("构建多模态序列...")
        
        # 获取数据维度
        N, seq_len, sst_channels, H, W = sst_sequences.shape
        era5_channels = len(interpolated_era5)
        total_channels = sst_channels + era5_channels
        
        # 创建多模态输入数组
        multi_modal_sequences = np.zeros((N, seq_len, total_channels, H, W))
        
        # 填充SST+IMF数据
        multi_modal_sequences[:, :, :sst_channels, :, :] = sst_sequences.numpy()
        
        # 填充ERA5数据
        for i, (var_name, var_data) in enumerate(interpolated_era5.items()):
            channel_idx = sst_channels + i
            
            # 为每个样本和时间步填充ERA5数据
            for sample_idx in range(N):
                for time_idx in range(seq_len):
                    # 计算对应的时间索引（假设时间对齐）
                    data_time_idx = sample_idx + time_idx
                    if data_time_idx < var_data.shape[0]:
                        multi_modal_sequences[sample_idx, time_idx, channel_idx, :, :] = var_data[data_time_idx]
        
        # 转换为PyTorch张量
        multi_modal_sequences = torch.from_numpy(multi_modal_sequences).float()
        
        # 创建通道名称
        sst_channel_names = sst_data['data_info']['channel_names']
        era5_channel_names = list(interpolated_era5.keys())
        all_channel_names = sst_channel_names + era5_channel_names
        
        # 创建数据集字典
        dataset = {
            'sequences': multi_modal_sequences,
            'targets': sst_targets,  # 目标仍然是SST
            'metadata': {
                'num_samples': N,
                'sequence_length': seq_len,
                'prediction_length': prediction_length,
                'total_channels': total_channels,
                'sst_channels': sst_channels,
                'era5_channels': era5_channels,
                'spatial_shape': (H, W),
                'channel_names': all_channel_names,
                'sst_channel_names': sst_channel_names,
                'era5_channel_names': era5_channel_names
            }
        }
        
        print(f"多模态数据集创建完成!")
        print(f"  输入形状: {multi_modal_sequences.shape}")
        print(f"  目标形状: {sst_targets.shape}")
        print(f"  总通道数: {total_channels} (SST+IMF: {sst_channels}, ERA5: {era5_channels})")
        print(f"  通道名称: {all_channel_names}")
        
        return dataset
    
    def save_dataset(self, dataset):
        """
        保存多模态数据集
        
        Args:
            dataset: 数据集字典
        """
        print("保存多模态数据集...")
        
        # 保存PyTorch格式
        torch.save(dataset, self.output_dir / 'multi_modal_dataset.pt')
        
        # 保存HDF5格式
        with h5py.File(self.output_dir / 'multi_modal_dataset.h5', 'w') as f:
            f.create_dataset('sequences', data=dataset['sequences'].numpy(), compression='gzip')
            f.create_dataset('targets', data=dataset['targets'].numpy(), compression='gzip')
            
            # 保存元数据属性
            for key, value in dataset['metadata'].items():
                if isinstance(value, (int, float, str)):
                    f.attrs[key] = value
                elif isinstance(value, (list, tuple)):
                    if all(isinstance(x, str) for x in value):
                        f.attrs[key] = [x.encode('utf-8') for x in value]
                    else:
                        f.attrs[key] = value
        
        # 保存JSON元数据
        with open(self.output_dir / 'multi_modal_metadata.json', 'w') as f:
            json.dump(dataset['metadata'], f, indent=2)
        
        print(f"数据集已保存到: {self.output_dir}")
        print("文件列表:")
        print("  - multi_modal_dataset.pt (PyTorch格式)")
        print("  - multi_modal_dataset.h5 (HDF5格式)")
        print("  - multi_modal_metadata.json (元数据)")
    
    def process_full_pipeline(self):
        """
        执行完整的多模态数据处理流程
        """
        print("="*60)
        print("多模态数据处理流程")
        print("="*60)
        
        # 创建数据集
        dataset = self.create_multi_modal_dataset()
        
        # 保存数据集
        self.save_dataset(dataset)
        
        print("="*60)
        print("多模态数据处理完成！")
        print("="*60)
        
        return dataset

def main():
    """
    主函数
    """
    processor = MultiModalDataProcessor()
    dataset = processor.process_full_pipeline()
    
    return dataset

if __name__ == "__main__":
    main()
