#!/usr/bin/env python3
"""
SST数据检查脚本
用于检查NetCDF格式的SST数据的变量信息、维度结构等
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path

def inspect_sst_data(file_path):
    """
    检查SST数据文件的详细信息
    
    Args:
        file_path (str): SST数据文件路径
    """
    print(f"正在检查文件: {file_path}")
    print("=" * 60)
    
    try:
        # 使用xarray读取NetCDF文件
        ds = xr.open_dataset(file_path)
        
        print("1. 数据集基本信息:")
        print(f"   文件大小: {Path(file_path).stat().st_size / (1024*1024):.2f} MB")
        print(f"   数据变量数量: {len(ds.data_vars)}")
        print(f"   坐标变量数量: {len(ds.coords)}")
        print(f"   维度数量: {len(ds.dims)}")
        
        print("\n2. 维度信息:")
        for dim_name, dim_size in ds.dims.items():
            print(f"   {dim_name}: {dim_size}")
        
        print("\n3. 坐标变量:")
        for coord_name, coord_var in ds.coords.items():
            print(f"   {coord_name}: {coord_var.dims}, shape={coord_var.shape}, dtype={coord_var.dtype}")
            if coord_var.size <= 10:
                print(f"      值: {coord_var.values}")
            else:
                print(f"      范围: {coord_var.min().values} 到 {coord_var.max().values}")
        
        print("\n4. 数据变量:")
        for var_name, var_data in ds.data_vars.items():
            print(f"   {var_name}:")
            print(f"      维度: {var_data.dims}")
            print(f"      形状: {var_data.shape}")
            print(f"      数据类型: {var_data.dtype}")
            print(f"      属性: {dict(var_data.attrs)}")
            
            # 统计信息
            if var_data.size > 0:
                valid_data = var_data.where(~np.isnan(var_data), drop=True)
                if valid_data.size > 0:
                    print(f"      数值范围: {float(valid_data.min()):.4f} 到 {float(valid_data.max()):.4f}")
                    print(f"      平均值: {float(valid_data.mean()):.4f}")
                    print(f"      标准差: {float(valid_data.std()):.4f}")
                    print(f"      缺失值数量: {var_data.isnull().sum().values}")
        
        print("\n5. 全局属性:")
        for attr_name, attr_value in ds.attrs.items():
            print(f"   {attr_name}: {attr_value}")
        
        # 如果是时间序列数据，显示时间信息
        if 'time' in ds.dims:
            time_coord = ds.coords['time']
            print(f"\n6. 时间信息:")
            print(f"   时间步数: {len(time_coord)}")
            print(f"   开始时间: {pd.to_datetime(time_coord.values[0])}")
            print(f"   结束时间: {pd.to_datetime(time_coord.values[-1])}")
            
            # 计算时间间隔
            if len(time_coord) > 1:
                time_diff = pd.to_datetime(time_coord.values[1]) - pd.to_datetime(time_coord.values[0])
                print(f"   时间间隔: {time_diff}")
        
        return ds
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def visualize_sst_sample(ds, var_name='sst', time_idx=0):
    """
    可视化SST数据样本
    
    Args:
        ds: xarray数据集
        var_name: 变量名
        time_idx: 时间索引
    """
    if var_name not in ds.data_vars:
        print(f"变量 {var_name} 不存在于数据集中")
        available_vars = list(ds.data_vars.keys())
        print(f"可用变量: {available_vars}")
        if available_vars:
            var_name = available_vars[0]
            print(f"使用第一个可用变量: {var_name}")
        else:
            return
    
    var_data = ds[var_name]
    
    # 根据数据维度选择合适的切片方式
    if len(var_data.dims) == 3:  # 时间 x 纬度 x 经度
        if 'time' in var_data.dims:
            data_slice = var_data.isel(time=time_idx)
        else:
            data_slice = var_data.isel({var_data.dims[0]: time_idx})
    elif len(var_data.dims) == 2:  # 纬度 x 经度
        data_slice = var_data
    else:
        print(f"不支持的数据维度: {var_data.dims}")
        return
    
    plt.figure(figsize=(12, 8))
    
    # 创建地图可视化
    if 'lat' in ds.coords and 'lon' in ds.coords:
        data_slice.plot(x='lon', y='lat', cmap='RdYlBu_r', 
                       cbar_kwargs={'label': f'{var_name} ({var_data.attrs.get("units", "")})'})
    else:
        data_slice.plot(cmap='RdYlBu_r',
                       cbar_kwargs={'label': f'{var_name} ({var_data.attrs.get("units", "")})'})
    
    plt.title(f'{var_name.upper()} 数据可视化 (时间索引: {time_idx})')
    plt.tight_layout()
    plt.savefig(f'sst_sample_{time_idx}.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"样本数据形状: {data_slice.shape}")
    print(f"样本数据统计:")
    print(f"  最小值: {float(data_slice.min()):.4f}")
    print(f"  最大值: {float(data_slice.max()):.4f}")
    print(f"  平均值: {float(data_slice.mean()):.4f}")
    print(f"  标准差: {float(data_slice.std()):.4f}")

if __name__ == "__main__":
    # 检查SST数据文件
    sst_files = ["SST-V2.nc", "data_ERA5.nc"]
    
    for file_path in sst_files:
        if Path(file_path).exists():
            print(f"\n{'='*80}")
            print(f"检查文件: {file_path}")
            print(f"{'='*80}")
            
            ds = inspect_sst_data(file_path)
            
            if ds is not None:
                # 可视化第一个时间步的数据
                try:
                    visualize_sst_sample(ds, time_idx=0)
                except Exception as e:
                    print(f"可视化时出错: {e}")
                
                ds.close()
        else:
            print(f"文件不存在: {file_path}")
