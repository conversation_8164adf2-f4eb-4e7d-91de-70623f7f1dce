#!/usr/bin/env python3
"""
使用模拟SST数据测试MEEMD分解流程
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import json
from meemd_decomposition import MEEMD2D

def generate_simulated_sst_data(T=50, H=20, W=30):
    """
    生成模拟的SST数据
    
    Args:
        T: 时间步数
        H: 纬度网格数
        W: 经度网格数
        
    Returns:
        sst_data: 模拟的SST数据 (T, H, W)
        metadata: 元数据
    """
    print(f"生成模拟SST数据: {T}×{H}×{W}")
    
    # 创建空间网格
    x = np.linspace(0, 2*np.pi, W)
    y = np.linspace(0, np.pi, H)
    t = np.linspace(0, 4*np.pi, T)
    
    X, Y = np.meshgrid(x, y)
    
    # 生成包含多个时空模式的SST数据
    sst_data = np.zeros((T, H, W))
    
    for i, time in enumerate(t):
        # 基础温度场（模拟纬度梯度）
        base_temp = 25 + 5 * np.cos(Y)
        
        # 季节性变化
        seasonal = 3 * np.sin(time/4) * np.ones((H, W))
        
        # 空间波动模式1（大尺度）
        pattern1 = 2 * np.sin(X) * np.cos(Y) * np.cos(time/2)
        
        # 空间波动模式2（中尺度）
        pattern2 = 1.5 * np.sin(2*X + time) * np.sin(Y)
        
        # 空间波动模式3（小尺度）
        pattern3 = 0.8 * np.sin(4*X) * np.sin(2*Y) * np.sin(time)
        
        # 随机噪声
        noise = 0.3 * np.random.randn(H, W)
        
        # 合成最终的SST场
        sst_data[i] = base_temp + seasonal + pattern1 + pattern2 + pattern3 + noise
    
    # 创建元数据
    metadata = {
        'shape': sst_data.shape,
        'time_range': ['2020-01-01', '2020-12-31'],
        'lat_range': [15.0, 25.0],
        'lon_range': [110.0, 120.0],
        'units': 'Celsius',
        'variable_name': 'simulated_sst',
        'spatial_resolution': {'lat': 0.5, 'lon': 0.5},
        'description': 'Simulated SST data for testing MEEMD decomposition'
    }
    
    print(f"数据范围: {sst_data.min():.2f} - {sst_data.max():.2f} °C")
    
    return sst_data, metadata

class SimulatedSSTProcessor:
    """
    模拟SST数据处理器
    """
    
    def __init__(self, output_dir="./processed_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化MEEMD分解器（使用较小的参数以加快测试速度）
        self.meemd = MEEMD2D(trials=20, noise_scale=0.005)
    
    def perform_meemd_decomposition(self, sst_data, sample_size=None):
        """
        执行MEEMD分解
        """
        T, H, W = sst_data.shape
        
        if sample_size is None:
            sample_size = T
        
        sample_size = min(sample_size, T)
        time_indices = range(sample_size)
        
        print(f"开始MEEMD分解...")
        print(f"处理时间步: 0 - {sample_size-1} (共{sample_size}步)")
        
        # 执行分解
        imf_data = self.meemd.decompose_3d_sst(sst_data, time_indices)
        
        # 创建分解元数据
        decomp_metadata = {
            'num_time_steps': sample_size,
            'num_imfs': imf_data.shape[1],
            'spatial_shape': (H, W),
            'time_indices': list(time_indices),
            'meemd_params': {
                'trials': self.meemd.trials,
                'noise_scale': self.meemd.noise_scale,
                'S_number': self.meemd.S_number,
                'num_siftings': self.meemd.num_siftings
            }
        }
        
        print(f"分解完成！得到{imf_data.shape[1]}个IMF分量")
        
        return imf_data, decomp_metadata
    
    def save_for_pytorch(self, sst_data, imf_data, metadata, decomp_metadata, 
                        sequence_length=5, prediction_length=1):
        """
        保存数据为适合PyTorch ConvLSTM训练的格式
        """
        print("保存数据为PyTorch格式...")
        
        T, num_imfs, H, W = imf_data.shape
        
        # 创建序列数据
        sequences = []
        targets = []
        
        for i in range(T - sequence_length - prediction_length + 1):
            # 输入序列：原始SST + IMF分量
            seq_sst = sst_data[i:i+sequence_length]  # (seq_len, H, W)
            seq_imfs = imf_data[i:i+sequence_length]  # (seq_len, num_imfs, H, W)
            
            # 合并为多通道输入 (seq_len, 1+num_imfs, H, W)
            seq_input = np.concatenate([
                seq_sst[:, np.newaxis, :, :],  # 添加通道维度
                seq_imfs
            ], axis=1)
            
            # 目标：未来的SST
            target = sst_data[i+sequence_length:i+sequence_length+prediction_length]
            
            sequences.append(seq_input)
            targets.append(target)
        
        sequences = np.array(sequences)  # (num_samples, seq_len, channels, H, W)
        targets = np.array(targets)      # (num_samples, pred_len, H, W)
        
        print(f"序列数据形状: {sequences.shape}")
        print(f"目标数据形状: {targets.shape}")
        
        # 转换为PyTorch张量
        sequences_tensor = torch.from_numpy(sequences).float()
        targets_tensor = torch.from_numpy(targets).float()
        
        # 保存为.pt文件
        torch.save({
            'sequences': sequences_tensor,
            'targets': targets_tensor,
            'metadata': metadata,
            'decomp_metadata': decomp_metadata,
            'data_info': {
                'num_samples': len(sequences),
                'sequence_length': sequence_length,
                'prediction_length': prediction_length,
                'num_channels': 1 + num_imfs,  # 原始SST + IMF分量
                'spatial_shape': (H, W),
                'channel_names': ['original_sst'] + [f'imf_{i+1}' for i in range(num_imfs)]
            }
        }, self.output_dir / 'simulated_sst_convlstm_data.pt')
        
        # 保存元数据为JSON
        full_metadata = {
            'original_metadata': metadata,
            'decomposition_metadata': decomp_metadata,
            'training_data_info': {
                'num_samples': len(sequences),
                'sequence_length': sequence_length,
                'prediction_length': prediction_length,
                'num_channels': 1 + num_imfs,
                'spatial_shape': [H, W],
                'channel_names': ['original_sst'] + [f'imf_{i+1}' for i in range(num_imfs)]
            }
        }
        
        with open(self.output_dir / 'simulated_metadata.json', 'w') as f:
            json.dump(full_metadata, f, indent=2)
        
        print(f"数据已保存到: {self.output_dir}")
        
        return sequences_tensor, targets_tensor

def visualize_results(sst_data, imf_data, save_dir="./plots"):
    """
    可视化分解结果
    """
    save_dir = Path(save_dir)
    save_dir.mkdir(exist_ok=True)
    
    T, num_imfs, H, W = imf_data.shape
    
    # 选择一个时间步进行可视化
    time_idx = T // 2
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    # 显示原始SST
    im0 = axes[0].imshow(sst_data[time_idx], cmap='RdYlBu_r', aspect='auto')
    axes[0].set_title(f'Original SST (t={time_idx})')
    plt.colorbar(im0, ax=axes[0], shrink=0.8)
    
    # 显示前几个IMF分量
    for i in range(min(4, num_imfs)):
        ax_idx = i + 1
        if ax_idx < len(axes):
            im = axes[ax_idx].imshow(imf_data[time_idx, i], cmap='RdYlBu_r', aspect='auto')
            axes[ax_idx].set_title(f'IMF {i+1}')
            plt.colorbar(im, ax=axes[ax_idx], shrink=0.8)
    
    # 显示重构信号
    if num_imfs > 0:
        reconstructed = np.sum(imf_data[time_idx], axis=0)
        im_rec = axes[5].imshow(reconstructed, cmap='RdYlBu_r', aspect='auto')
        axes[5].set_title('Reconstructed')
        plt.colorbar(im_rec, ax=axes[5], shrink=0.8)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'simulated_meemd_results.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """
    主测试函数
    """
    print("="*60)
    print("模拟SST数据MEEMD分解测试")
    print("="*60)
    
    # 1. 生成模拟数据
    sst_data, metadata = generate_simulated_sst_data(T=30, H=20, W=25)
    
    # 2. 创建处理器并执行分解
    processor = SimulatedSSTProcessor()
    imf_data, decomp_metadata = processor.perform_meemd_decomposition(sst_data, sample_size=15)
    
    # 3. 保存为PyTorch格式
    sequences, targets = processor.save_for_pytorch(
        sst_data[:15], imf_data, metadata, decomp_metadata,
        sequence_length=5, prediction_length=1
    )
    
    # 4. 可视化结果
    visualize_results(sst_data[:15], imf_data)
    
    print("\n="*60)
    print("测试完成！")
    print(f"生成的训练数据:")
    print(f"  输入序列形状: {sequences.shape}")
    print(f"  目标数据形状: {targets.shape}")
    print(f"  通道数: {sequences.shape[2]} (原始SST + {sequences.shape[2]-1}个IMF)")
    print("="*60)

if __name__ == "__main__":
    main()
