#!/usr/bin/env python3
"""
简化的ConvLSTM训练测试脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time
from tqdm import tqdm

# 简化的ConvLSTM模型
class SimpleConvLSTM(nn.Module):
    """
    简化的ConvLSTM模型
    """
    
    def __init__(self, input_channels, hidden_channels=32, output_channels=1):
        super(SimpleConvLSTM, self).__init__()
        
        # 输入卷积层
        self.input_conv = nn.Conv2d(input_channels, hidden_channels, 3, padding=1)
        
        # 简化的时序处理（使用3D卷积代替ConvLSTM）
        self.temporal_conv = nn.Conv3d(hidden_channels, hidden_channels, (3, 3, 3), padding=(1, 1, 1))
        
        # 输出层
        self.output_conv = nn.Sequential(
            nn.Conv2d(hidden_channels, hidden_channels//2, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels//2, output_channels, 1)
        )
        
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch, seq_len, channels, height, width)
        """
        batch_size, seq_len, channels, height, width = x.shape
        
        # 处理每个时间步
        temporal_features = []
        for t in range(seq_len):
            # 空间特征提取
            spatial_feat = self.relu(self.input_conv(x[:, t]))
            temporal_features.append(spatial_feat)
        
        # 堆叠时间维度
        temporal_stack = torch.stack(temporal_features, dim=2)  # (batch, hidden, seq_len, H, W)
        
        # 时序卷积
        temporal_out = self.relu(self.temporal_conv(temporal_stack))
        
        # 取最后一个时间步
        final_features = temporal_out[:, :, -1, :, :]  # (batch, hidden, H, W)
        
        # 输出预测
        output = self.output_conv(final_features)
        
        return output

def load_data(data_path="./multi_modal_data/multi_modal_dataset.pt"):
    """
    加载多模态数据
    """
    print("加载数据...")
    
    if not Path(data_path).exists():
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    dataset = torch.load(data_path, weights_only=False)
    
    sequences = dataset['sequences']
    targets = dataset['targets']
    metadata = dataset['metadata']
    
    print(f"数据形状:")
    print(f"  输入: {sequences.shape}")
    print(f"  目标: {targets.shape}")
    print(f"  通道数: {metadata['total_channels']}")
    print(f"  通道名称: {metadata['channel_names']}")
    
    return sequences, targets, metadata

def create_data_loaders(sequences, targets, batch_size=2, train_ratio=0.7):
    """
    创建数据加载器
    """
    print("创建数据加载器...")
    
    # 创建数据集
    dataset = TensorDataset(sequences, targets)
    
    # 划分数据集
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = total_size - train_size
    
    train_dataset, val_dataset = random_split(
        dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
    
    print(f"数据集划分:")
    print(f"  训练集: {len(train_dataset)} 样本")
    print(f"  验证集: {len(val_dataset)} 样本")
    print(f"  批次大小: {batch_size}")
    
    return train_loader, val_loader

def train_model(model, train_loader, val_loader, num_epochs=10, lr=0.001, device='cpu'):
    """
    训练模型
    """
    print(f"开始训练模型 (设备: {device})...")
    
    model = model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    train_losses = []
    val_losses = []
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_batches = 0
        
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        pbar = tqdm(train_loader, desc="训练")
        
        for sequences, targets in pbar:
            sequences = sequences.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(sequences)
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / train_batches
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_batches = 0
        
        with torch.no_grad():
            for sequences, targets in val_loader:
                sequences = sequences.to(device)
                targets = targets.to(device)
                
                outputs = model(sequences)
                loss = criterion(outputs, targets)
                
                val_loss += loss.item()
                val_batches += 1
        
        avg_val_loss = val_loss / val_batches
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f"  训练损失: {avg_train_loss:.6f}")
        print(f"  验证损失: {avg_val_loss:.6f}")
    
    return train_losses, val_losses

def test_model(model, test_sequences, test_targets, device='cpu'):
    """
    测试模型
    """
    print("测试模型...")
    
    model = model.to(device)
    model.eval()
    
    criterion = nn.MSELoss()
    
    with torch.no_grad():
        test_sequences = test_sequences.to(device)
        test_targets = test_targets.to(device)
        
        outputs = model(test_sequences)
        test_loss = criterion(outputs, test_targets)
        
        # 计算评估指标
        mse = torch.mean((outputs - test_targets) ** 2).item()
        mae = torch.mean(torch.abs(outputs - test_targets)).item()
        rmse = torch.sqrt(torch.tensor(mse)).item()
        
        # 转换为numpy进行相关性计算
        pred_np = outputs.cpu().numpy().flatten()
        target_np = test_targets.cpu().numpy().flatten()
        correlation = np.corrcoef(pred_np, target_np)[0, 1]
    
    print(f"测试结果:")
    print(f"  测试损失: {test_loss:.6f}")
    print(f"  MSE: {mse:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  RMSE: {rmse:.6f}")
    print(f"  相关系数: {correlation:.6f}")
    
    return {
        'test_loss': test_loss.item(),
        'mse': mse,
        'mae': mae,
        'rmse': rmse,
        'correlation': correlation,
        'predictions': outputs.cpu().numpy(),
        'targets': test_targets.cpu().numpy()
    }

def visualize_results(train_losses, val_losses, test_results):
    """
    可视化结果
    """
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 训练曲线
    epochs = range(1, len(train_losses) + 1)
    axes[0].plot(epochs, train_losses, 'b-', label='训练损失')
    axes[0].plot(epochs, val_losses, 'r-', label='验证损失')
    axes[0].set_title('训练曲线')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 预测vs真实值散点图
    predictions = test_results['predictions'].flatten()
    targets = test_results['targets'].flatten()
    
    axes[1].scatter(targets, predictions, alpha=0.5, s=1)
    axes[1].plot([targets.min(), targets.max()], [targets.min(), targets.max()], 'r--', lw=2)
    axes[1].set_title(f'预测 vs 真实值\n相关系数: {test_results["correlation"]:.4f}')
    axes[1].set_xlabel('真实值')
    axes[1].set_ylabel('预测值')
    axes[1].grid(True, alpha=0.3)
    
    # 预测示例
    sample_idx = 0
    pred_sample = test_results['predictions'][sample_idx, 0]
    target_sample = test_results['targets'][sample_idx, 0]
    
    im = axes[2].imshow(pred_sample - target_sample, cmap='RdBu', vmin=-2, vmax=2)
    axes[2].set_title('预测误差示例')
    plt.colorbar(im, ax=axes[2], shrink=0.8)
    
    plt.tight_layout()
    plt.savefig('training_results.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    print("="*60)
    print("简化ConvLSTM模型训练测试")
    print("="*60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    try:
        # 1. 加载数据
        sequences, targets, metadata = load_data()
        
        # 2. 创建数据加载器
        train_loader, val_loader = create_data_loaders(sequences, targets, batch_size=2)
        
        # 3. 创建模型
        input_channels = metadata['total_channels']
        model = SimpleConvLSTM(input_channels=input_channels, hidden_channels=32)
        
        print(f"\n模型信息:")
        print(f"  输入通道数: {input_channels}")
        total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"  参数数量: {total_params:,}")
        
        # 4. 训练模型
        train_losses, val_losses = train_model(
            model, train_loader, val_loader, 
            num_epochs=5, lr=0.001, device=device
        )
        
        # 5. 测试模型
        # 使用验证集作为测试集
        test_sequences = sequences[-10:]  # 取最后10个样本作为测试
        test_targets = targets[-10:]
        
        test_results = test_model(model, test_sequences, test_targets, device)
        
        # 6. 可视化结果
        visualize_results(train_losses, val_losses, test_results)
        
        print("\n" + "="*60)
        print("训练测试完成！")
        print("="*60)
        
    except Exception as e:
        print(f"运行时错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
