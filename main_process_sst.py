#!/usr/bin/env python3
"""
SST数据MEEMD分解主程序
"""

import argparse
import sys
from pathlib import Path
from sst_data_processor import SSTDataProcessor

def main():
    parser = argparse.ArgumentParser(description='SST数据MEEMD分解和预处理')
    parser.add_argument('--sst_file', type=str, default='SST-V2.nc',
                       help='SST数据文件路径')
    parser.add_argument('--output_dir', type=str, default='./processed_data',
                       help='输出目录')
    parser.add_argument('--sample_size', type=int, default=100,
                       help='处理的时间步数量（用于测试，设为None处理全部数据）')
    parser.add_argument('--sequence_length', type=int, default=14,
                       help='输入序列长度')
    parser.add_argument('--prediction_length', type=int, default=1,
                       help='预测长度')
    parser.add_argument('--full_data', action='store_true',
                       help='处理全部数据（忽略sample_size）')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not Path(args.sst_file).exists():
        print(f"错误: SST数据文件不存在: {args.sst_file}")
        sys.exit(1)
    
    # 创建数据处理器
    processor = SSTDataProcessor(args.sst_file, args.output_dir)
    
    # 确定处理的数据量
    sample_size = None if args.full_data else args.sample_size
    
    print(f"配置参数:")
    print(f"  SST文件: {args.sst_file}")
    print(f"  输出目录: {args.output_dir}")
    print(f"  处理数据量: {'全部' if sample_size is None else sample_size}")
    print(f"  序列长度: {args.sequence_length}")
    print(f"  预测长度: {args.prediction_length}")
    
    try:
        # 执行完整处理流程
        sequences, targets = processor.process_full_pipeline(
            sample_size=sample_size,
            sequence_length=args.sequence_length,
            prediction_length=args.prediction_length
        )
        
        print("\n处理成功完成！")
        print(f"生成的训练数据:")
        print(f"  输入序列形状: {sequences.shape}")
        print(f"  目标数据形状: {targets.shape}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
