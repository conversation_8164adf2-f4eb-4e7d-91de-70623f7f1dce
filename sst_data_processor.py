#!/usr/bin/env python3
"""
SST数据处理器
负责加载SST数据，进行MEEMD分解，并保存为适合PyTorch ConvLSTM训练的格式
"""

import numpy as np
import torch
import h5py
import pickle
from pathlib import Path
from datetime import datetime
import json
from meemd_decomposition import MEEMD2D
from tqdm import tqdm

# 延迟导入，在实际使用时再检测
HAS_XARRAY = None
HAS_NETCDF4 = None

def check_xarray():
    """检查xarray是否可用"""
    global HAS_XARRAY
    if HAS_XARRAY is None:
        try:
            import xarray as xr
            HAS_XARRAY = True
        except ImportError:
            HAS_XARRAY = False
            print("警告: xarray导入失败")
    return HAS_XARRAY

def check_netcdf4():
    """检查netCDF4是否可用"""
    global HAS_NETCDF4
    if HAS_NETCDF4 is None:
        try:
            import netCDF4 as nc
            HAS_NETCDF4 = True
        except ImportError:
            HAS_NETCDF4 = False
            print("警告: netCDF4导入失败")
    return HAS_NETCDF4

class SSTDataProcessor:
    """
    SST数据处理器
    """
    
    def __init__(self, sst_file_path, output_dir="./processed_data"):
        """
        初始化数据处理器
        
        Args:
            sst_file_path: SST数据文件路径
            output_dir: 输出目录
        """
        self.sst_file_path = sst_file_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化MEEMD分解器
        self.meemd = MEEMD2D(trials=50, noise_scale=0.005)
        
    def load_sst_data(self):
        """
        加载SST数据

        Returns:
            sst_data: SST数据数组 (T, H, W)
            metadata: 元数据字典
        """
        print(f"加载SST数据: {self.sst_file_path}")

        # 尝试多种方法读取数据
        methods = [
            ("h5py", self._load_with_h5py),
            ("netCDF4", self._load_with_netcdf4),
            ("xarray", self._load_with_xarray)
        ]

        for method_name, method_func in methods:
            try:
                print(f"尝试使用 {method_name} 读取数据...")
                return method_func()
            except Exception as e:
                print(f"{method_name} 读取失败: {e}")
                continue

        raise ImportError("所有数据读取方法都失败了，请检查数据文件和依赖库")

    def _load_with_h5py(self):
        """使用h5py读取NetCDF文件（NetCDF4格式实际上是HDF5）"""
        import h5py

        with h5py.File(self.sst_file_path, 'r') as f:
            # 读取SST数据
            sst_var = f['analysed_sst']
            sst_data = sst_var[:]  # (T, H, W)

            # 读取坐标数据
            time_var = f['time']
            lat_var = f['latitude']
            lon_var = f['longitude']

            # 数据预处理：转换单位从开尔文到摄氏度
            sst_data = sst_data - 273.15

            # 创建元数据
            metadata = {
                'shape': sst_data.shape,
                'time_range': [str(time_var[0]), str(time_var[-1])],
                'lat_range': [float(lat_var[:].min()), float(lat_var[:].max())],
                'lon_range': [float(lon_var[:].min()), float(lon_var[:].max())],
                'units': 'Celsius',
                'original_units': 'Kelvin',
                'variable_name': 'analysed_sst',
                'spatial_resolution': {
                    'lat': float(lat_var[1] - lat_var[0]),
                    'lon': float(lon_var[1] - lon_var[0])
                }
            }

        print(f"数据形状: {sst_data.shape}")
        print(f"数据范围: {sst_data.min():.2f} - {sst_data.max():.2f} °C")

        return sst_data, metadata

    def _load_with_xarray(self):
        """使用xarray加载数据"""
        import xarray as xr

        ds = xr.open_dataset(self.sst_file_path)

        # 获取SST变量（根据之前的检查，变量名是analysed_sst）
        sst_var = ds['analysed_sst']

        # 转换为numpy数组
        sst_data = sst_var.values  # (T, H, W)

        # 数据预处理：转换单位从开尔文到摄氏度
        sst_data = sst_data - 273.15

        # 创建元数据
        metadata = {
            'shape': sst_data.shape,
            'time_range': [str(ds.time.values[0]), str(ds.time.values[-1])],
            'lat_range': [float(ds.latitude.min()), float(ds.latitude.max())],
            'lon_range': [float(ds.longitude.min()), float(ds.longitude.max())],
            'units': 'Celsius',
            'original_units': 'Kelvin',
            'variable_name': 'analysed_sst',
            'spatial_resolution': {
                'lat': float(ds.latitude[1] - ds.latitude[0]),
                'lon': float(ds.longitude[1] - ds.longitude[0])
            }
        }

        ds.close()

        print(f"数据形状: {sst_data.shape}")
        print(f"数据范围: {sst_data.min():.2f} - {sst_data.max():.2f} °C")

        return sst_data, metadata

    def _load_with_netcdf4(self):
        """使用netCDF4加载数据"""
        import netCDF4 as nc

        with nc.Dataset(self.sst_file_path, 'r') as ds:
            # 获取SST变量
            sst_var = ds.variables['analysed_sst']
            sst_data = sst_var[:]  # (T, H, W)

            # 获取坐标变量
            time_var = ds.variables['time']
            lat_var = ds.variables['latitude']
            lon_var = ds.variables['longitude']

            # 数据预处理：转换单位从开尔文到摄氏度
            sst_data = sst_data - 273.15

            # 创建元数据
            metadata = {
                'shape': sst_data.shape,
                'time_range': [str(time_var[0]), str(time_var[-1])],
                'lat_range': [float(lat_var[:].min()), float(lat_var[:].max())],
                'lon_range': [float(lon_var[:].min()), float(lon_var[:].max())],
                'units': 'Celsius',
                'original_units': 'Kelvin',
                'variable_name': 'analysed_sst',
                'spatial_resolution': {
                    'lat': float(lat_var[1] - lat_var[0]),
                    'lon': float(lon_var[1] - lon_var[0])
                }
            }

        print(f"数据形状: {sst_data.shape}")
        print(f"数据范围: {sst_data.min():.2f} - {sst_data.max():.2f} °C")

        return sst_data, metadata
    
    def perform_meemd_decomposition(self, sst_data, sample_size=None, start_idx=0):
        """
        执行MEEMD分解
        
        Args:
            sst_data: SST数据 (T, H, W)
            sample_size: 处理的时间步数量，None表示处理所有
            start_idx: 开始索引
            
        Returns:
            imf_data: IMF分量数据 (T, num_imfs, H, W)
            decomp_metadata: 分解元数据
        """
        T, H, W = sst_data.shape
        
        if sample_size is None:
            sample_size = T
        
        end_idx = min(start_idx + sample_size, T)
        time_indices = range(start_idx, end_idx)
        
        print(f"开始MEEMD分解...")
        print(f"处理时间步: {start_idx} - {end_idx-1} (共{len(time_indices)}步)")
        
        # 执行分解
        imf_data = self.meemd.decompose_3d_sst(sst_data, time_indices)
        
        # 创建分解元数据
        decomp_metadata = {
            'num_time_steps': len(time_indices),
            'num_imfs': imf_data.shape[1],
            'spatial_shape': (H, W),
            'time_indices': list(time_indices),
            'meemd_params': {
                'trials': self.meemd.trials,
                'noise_scale': self.meemd.noise_scale,
                'S_number': self.meemd.S_number,
                'num_siftings': self.meemd.num_siftings
            },
            'decomposition_date': datetime.now().isoformat()
        }
        
        print(f"分解完成！得到{imf_data.shape[1]}个IMF分量")
        
        return imf_data, decomp_metadata
    
    def save_for_pytorch(self, sst_data, imf_data, metadata, decomp_metadata, 
                        sequence_length=10, prediction_length=1):
        """
        保存数据为适合PyTorch ConvLSTM训练的格式
        
        Args:
            sst_data: 原始SST数据
            imf_data: IMF分量数据 (T, num_imfs, H, W)
            metadata: 原始数据元数据
            decomp_metadata: 分解元数据
            sequence_length: 输入序列长度
            prediction_length: 预测长度
        """
        print("保存数据为PyTorch格式...")
        
        T, num_imfs, H, W = imf_data.shape
        
        # 创建序列数据
        sequences = []
        targets = []
        
        for i in range(T - sequence_length - prediction_length + 1):
            # 输入序列：原始SST + IMF分量
            seq_sst = sst_data[i:i+sequence_length]  # (seq_len, H, W)
            seq_imfs = imf_data[i:i+sequence_length]  # (seq_len, num_imfs, H, W)
            
            # 合并为多通道输入 (seq_len, 1+num_imfs, H, W)
            seq_input = np.concatenate([
                seq_sst[:, np.newaxis, :, :],  # 添加通道维度
                seq_imfs
            ], axis=1)
            
            # 目标：未来的SST
            target = sst_data[i+sequence_length:i+sequence_length+prediction_length]
            
            sequences.append(seq_input)
            targets.append(target)
        
        sequences = np.array(sequences)  # (num_samples, seq_len, channels, H, W)
        targets = np.array(targets)      # (num_samples, pred_len, H, W)
        
        print(f"序列数据形状: {sequences.shape}")
        print(f"目标数据形状: {targets.shape}")
        
        # 转换为PyTorch张量
        sequences_tensor = torch.from_numpy(sequences).float()
        targets_tensor = torch.from_numpy(targets).float()
        
        # 保存为.pt文件
        torch.save({
            'sequences': sequences_tensor,
            'targets': targets_tensor,
            'metadata': metadata,
            'decomp_metadata': decomp_metadata,
            'data_info': {
                'num_samples': len(sequences),
                'sequence_length': sequence_length,
                'prediction_length': prediction_length,
                'num_channels': 1 + num_imfs,  # 原始SST + IMF分量
                'spatial_shape': (H, W),
                'channel_names': ['original_sst'] + [f'imf_{i+1}' for i in range(num_imfs)]
            }
        }, self.output_dir / 'sst_convlstm_data.pt')
        
        # 同时保存为HDF5格式（便于其他工具使用）
        with h5py.File(self.output_dir / 'sst_convlstm_data.h5', 'w') as f:
            f.create_dataset('sequences', data=sequences, compression='gzip')
            f.create_dataset('targets', data=targets, compression='gzip')
            
            # 保存元数据
            f.attrs['num_samples'] = len(sequences)
            f.attrs['sequence_length'] = sequence_length
            f.attrs['prediction_length'] = prediction_length
            f.attrs['num_channels'] = 1 + num_imfs
            f.attrs['spatial_height'] = H
            f.attrs['spatial_width'] = W
        
        # 保存详细元数据为JSON
        full_metadata = {
            'original_metadata': metadata,
            'decomposition_metadata': decomp_metadata,
            'training_data_info': {
                'num_samples': len(sequences),
                'sequence_length': sequence_length,
                'prediction_length': prediction_length,
                'num_channels': 1 + num_imfs,
                'spatial_shape': [H, W],
                'channel_names': ['original_sst'] + [f'imf_{i+1}' for i in range(num_imfs)],
                'data_range': {
                    'sequences_min': float(sequences.min()),
                    'sequences_max': float(sequences.max()),
                    'targets_min': float(targets.min()),
                    'targets_max': float(targets.max())
                }
            }
        }
        
        with open(self.output_dir / 'metadata.json', 'w') as f:
            json.dump(full_metadata, f, indent=2)
        
        print(f"数据已保存到: {self.output_dir}")
        print("文件列表:")
        print(f"  - sst_convlstm_data.pt (PyTorch格式)")
        print(f"  - sst_convlstm_data.h5 (HDF5格式)")
        print(f"  - metadata.json (元数据)")
        
        return sequences_tensor, targets_tensor
    
    def process_full_pipeline(self, sample_size=100, sequence_length=10, prediction_length=1):
        """
        执行完整的数据处理流程
        
        Args:
            sample_size: 处理的时间步数量
            sequence_length: 输入序列长度
            prediction_length: 预测长度
        """
        print("="*60)
        print("开始SST数据处理流程")
        print("="*60)
        
        # 1. 加载数据
        sst_data, metadata = self.load_sst_data()
        
        # 2. MEEMD分解
        imf_data, decomp_metadata = self.perform_meemd_decomposition(
            sst_data, sample_size=sample_size
        )
        
        # 3. 保存为PyTorch格式
        sequences, targets = self.save_for_pytorch(
            sst_data[:sample_size], imf_data, metadata, decomp_metadata,
            sequence_length, prediction_length
        )
        
        print("="*60)
        print("数据处理完成！")
        print("="*60)
        
        return sequences, targets
