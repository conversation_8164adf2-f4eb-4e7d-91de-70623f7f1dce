#!/usr/bin/env python3
"""
ConvLSTM模型训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import time
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

from convlstm_model import MultiModalSSTPredictor, count_parameters
from multi_modal_data_processor import MultiModalDataProcessor

class SSTTrainer:
    """
    SST预测模型训练器
    """
    
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        """
        初始化训练器
        
        Args:
            model: 模型
            device: 设备
        """
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def prepare_data(self, data_path="./multi_modal_data/multi_modal_dataset.pt", 
                    batch_size=4, train_ratio=0.7, val_ratio=0.2):
        """
        准备训练数据
        
        Args:
            data_path: 数据路径
            batch_size: 批次大小
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            
        Returns:
            train_loader, val_loader, test_loader: 数据加载器
        """
        print("准备训练数据...")
        
        # 检查数据文件是否存在
        if not Path(data_path).exists():
            print("多模态数据不存在，开始创建...")
            processor = MultiModalDataProcessor()
            dataset = processor.process_full_pipeline()
        else:
            print("加载现有多模态数据...")
            dataset = torch.load(data_path, weights_only=False)
        
        sequences = dataset['sequences']
        targets = dataset['targets']
        metadata = dataset['metadata']
        
        print(f"数据形状:")
        print(f"  输入: {sequences.shape}")
        print(f"  目标: {targets.shape}")
        print(f"  通道: {metadata['channel_names']}")
        
        # 创建数据集
        full_dataset = TensorDataset(sequences, targets)
        
        # 划分数据集
        total_size = len(full_dataset)
        train_size = int(train_ratio * total_size)
        val_size = int(val_ratio * total_size)
        test_size = total_size - train_size - val_size
        
        train_dataset, val_dataset, test_dataset = random_split(
            full_dataset, [train_size, val_size, test_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, drop_last=True)
        
        print(f"数据集划分:")
        print(f"  训练集: {len(train_dataset)} 样本")
        print(f"  验证集: {len(val_dataset)} 样本")
        print(f"  测试集: {len(test_dataset)} 样本")
        
        self.metadata = metadata
        return train_loader, val_loader, test_loader
    
    def train_epoch(self, train_loader, optimizer, criterion):
        """
        训练一个epoch
        """
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="训练")
        for batch_idx, (sequences, targets) in enumerate(pbar):
            sequences = sequences.to(self.device)
            targets = targets.to(self.device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(sequences)
            
            # 计算损失
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            pbar.set_postfix({'loss': f'{loss.item():.6f}'})
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate(self, val_loader, criterion):
        """
        验证模型
        """
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for sequences, targets in val_loader:
                sequences = sequences.to(self.device)
                targets = targets.to(self.device)
                
                outputs = self.model(sequences)
                loss = criterion(outputs, targets)
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def train(self, train_loader, val_loader, num_epochs=50, lr=0.001, 
              weight_decay=1e-4, save_dir="./checkpoints"):
        """
        训练模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs: 训练轮数
            lr: 学习率
            weight_decay: 权重衰减
            save_dir: 保存目录
        """
        print("开始训练...")
        
        # 创建保存目录
        save_dir = Path(save_dir)
        save_dir.mkdir(exist_ok=True)
        
        # 优化器和损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, factor=0.5)
        
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 10
        
        print(f"模型参数数量: {count_parameters(self.model):,}")
        print(f"训练设备: {self.device}")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss = self.train_epoch(train_loader, optimizer, criterion)
            
            # 验证
            val_loss = self.validate(val_loader, criterion)
            
            # 学习率调度
            scheduler.step(val_loss)
            
            # 记录损失
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch+1}/{num_epochs}")
            print(f"  训练损失: {train_loss:.6f}")
            print(f"  验证损失: {val_loss:.6f}")
            print(f"  学习率: {optimizer.param_groups[0]['lr']:.2e}")
            print(f"  耗时: {epoch_time:.2f}s")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                    'metadata': self.metadata
                }, save_dir / 'best_model.pth')
                print(f"  保存最佳模型 (验证损失: {val_loss:.6f})")
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= patience:
                print(f"验证损失连续{patience}轮未改善，提前停止训练")
                break
            
            print("-" * 50)
        
        print("训练完成!")
        return best_val_loss
    
    def test(self, test_loader, model_path="./checkpoints/best_model.pth"):
        """
        测试模型
        
        Args:
            test_loader: 测试数据加载器
            model_path: 模型路径
            
        Returns:
            test_results: 测试结果
        """
        print("开始测试...")
        
        # 加载最佳模型
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        self.model.eval()
        criterion = nn.MSELoss()
        
        total_loss = 0
        predictions = []
        targets_list = []
        
        with torch.no_grad():
            for sequences, targets in tqdm(test_loader, desc="测试"):
                sequences = sequences.to(self.device)
                targets = targets.to(self.device)
                
                outputs = self.model(sequences)
                loss = criterion(outputs, targets)
                
                total_loss += loss.item()
                
                # 收集预测结果
                predictions.append(outputs.cpu().numpy())
                targets_list.append(targets.cpu().numpy())
        
        avg_test_loss = total_loss / len(test_loader)
        
        # 合并所有预测结果
        predictions = np.concatenate(predictions, axis=0)
        targets_array = np.concatenate(targets_list, axis=0)
        
        # 计算评估指标
        mse = np.mean((predictions - targets_array) ** 2)
        mae = np.mean(np.abs(predictions - targets_array))
        rmse = np.sqrt(mse)
        
        # 计算相关系数
        pred_flat = predictions.flatten()
        target_flat = targets_array.flatten()
        correlation = np.corrcoef(pred_flat, target_flat)[0, 1]
        
        test_results = {
            'test_loss': avg_test_loss,
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'correlation': correlation,
            'predictions': predictions,
            'targets': targets_array
        }
        
        print(f"测试结果:")
        print(f"  测试损失: {avg_test_loss:.6f}")
        print(f"  MSE: {mse:.6f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  RMSE: {rmse:.6f}")
        print(f"  相关系数: {correlation:.6f}")
        
        return test_results
    
    def plot_training_curves(self, save_path="./training_curves.png"):
        """
        绘制训练曲线
        """
        plt.figure(figsize=(10, 6))
        
        epochs = range(1, len(self.train_losses) + 1)
        plt.plot(epochs, self.train_losses, 'b-', label='Training Loss')
        plt.plot(epochs, self.val_losses, 'r-', label='Validation Loss')
        
        plt.title('Training and Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.show()

def main():
    """
    主训练函数
    """
    print("="*60)
    print("SST ConvLSTM模型训练")
    print("="*60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = MultiModalSSTPredictor(
        sst_channels=7,    # 1个原始SST + 6个IMF
        era5_channels=4,   # u10, v10, t2m, msl
        hidden_channels=[64, 32, 16],
        kernel_sizes=[3, 3, 3],
        fusion_channels=32,
        dropout=0.1
    )
    
    # 创建训练器
    trainer = SSTTrainer(model, device)
    
    # 准备数据
    train_loader, val_loader, test_loader = trainer.prepare_data(batch_size=2)  # 小批次以适应内存
    
    # 训练模型
    best_val_loss = trainer.train(
        train_loader, val_loader, 
        num_epochs=30, lr=0.001, weight_decay=1e-4
    )
    
    # 绘制训练曲线
    trainer.plot_training_curves()
    
    # 测试模型
    test_results = trainer.test(test_loader)
    
    print("="*60)
    print("训练和测试完成!")
    print("="*60)

if __name__ == "__main__":
    main()
