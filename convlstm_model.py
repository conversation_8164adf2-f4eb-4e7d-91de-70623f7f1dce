#!/usr/bin/env python3
"""
ConvLSTM模型实现
用于SST预测的多模态ConvLSTM模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ConvLSTMCell(nn.Module):
    """
    ConvLSTM单元
    """
    
    def __init__(self, input_channels, hidden_channels, kernel_size, bias=True):
        """
        初始化ConvLSTM单元
        
        Args:
            input_channels: 输入通道数
            hidden_channels: 隐藏状态通道数
            kernel_size: 卷积核大小
            bias: 是否使用偏置
        """
        super(ConvLSTMCell, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2
        self.bias = bias
        
        # 输入到状态的卷积
        self.conv_input = nn.Conv2d(
            input_channels + hidden_channels, 
            4 * hidden_channels,  # i, f, o, g gates
            kernel_size, 
            padding=self.padding, 
            bias=bias
        )
    
    def forward(self, input_tensor, cur_state):
        """
        前向传播
        
        Args:
            input_tensor: 输入张量 (batch, channels, height, width)
            cur_state: 当前状态 (h, c)
            
        Returns:
            new_h, new_c: 新的隐藏状态和细胞状态
        """
        h_cur, c_cur = cur_state
        
        # 连接输入和隐藏状态
        combined = torch.cat([input_tensor, h_cur], dim=1)
        
        # 计算门控
        combined_conv = self.conv_input(combined)
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_channels, dim=1)
        
        i = torch.sigmoid(cc_i)  # 输入门
        f = torch.sigmoid(cc_f)  # 遗忘门
        o = torch.sigmoid(cc_o)  # 输出门
        g = torch.tanh(cc_g)     # 候选值
        
        # 更新细胞状态和隐藏状态
        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next
    
    def init_hidden(self, batch_size, image_size):
        """
        初始化隐藏状态
        
        Args:
            batch_size: 批次大小
            image_size: 图像尺寸 (height, width)
            
        Returns:
            初始隐藏状态和细胞状态
        """
        height, width = image_size
        device = next(self.parameters()).device
        
        h = torch.zeros(batch_size, self.hidden_channels, height, width, device=device)
        c = torch.zeros(batch_size, self.hidden_channels, height, width, device=device)
        
        return h, c

class ConvLSTM(nn.Module):
    """
    ConvLSTM网络
    """
    
    def __init__(self, input_channels, hidden_channels_list, kernel_size_list, 
                 num_layers, batch_first=True, bias=True, return_all_layers=False):
        """
        初始化ConvLSTM网络
        
        Args:
            input_channels: 输入通道数
            hidden_channels_list: 各层隐藏通道数列表
            kernel_size_list: 各层卷积核大小列表
            num_layers: 层数
            batch_first: 是否batch在第一维
            bias: 是否使用偏置
            return_all_layers: 是否返回所有层的输出
        """
        super(ConvLSTM, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_channels_list = hidden_channels_list
        self.kernel_size_list = kernel_size_list
        self.num_layers = num_layers
        self.batch_first = batch_first
        self.bias = bias
        self.return_all_layers = return_all_layers
        
        # 构建ConvLSTM层
        cell_list = []
        for i in range(num_layers):
            cur_input_channels = input_channels if i == 0 else hidden_channels_list[i-1]
            cell_list.append(ConvLSTMCell(
                input_channels=cur_input_channels,
                hidden_channels=hidden_channels_list[i],
                kernel_size=kernel_size_list[i],
                bias=bias
            ))
        
        self.cell_list = nn.ModuleList(cell_list)
    
    def forward(self, input_tensor, hidden_state=None):
        """
        前向传播
        
        Args:
            input_tensor: 输入张量 (batch, time, channels, height, width)
            hidden_state: 初始隐藏状态
            
        Returns:
            layer_output_list: 各层输出列表
            last_state_list: 最后状态列表
        """
        if not self.batch_first:
            # (time, batch, channels, height, width) -> (batch, time, channels, height, width)
            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)
        
        batch_size, seq_len, _, height, width = input_tensor.size()
        
        # 初始化隐藏状态
        if hidden_state is None:
            hidden_state = self._init_hidden(batch_size, (height, width))
        
        layer_output_list = []
        last_state_list = []
        
        cur_layer_input = input_tensor
        
        for layer_idx in range(self.num_layers):
            h, c = hidden_state[layer_idx]
            output_inner = []
            
            for t in range(seq_len):
                h, c = self.cell_list[layer_idx](cur_layer_input[:, t, :, :, :], (h, c))
                output_inner.append(h)
            
            layer_output = torch.stack(output_inner, dim=1)
            cur_layer_input = layer_output
            
            layer_output_list.append(layer_output)
            last_state_list.append((h, c))
        
        if not self.return_all_layers:
            layer_output_list = layer_output_list[-1:]
            last_state_list = last_state_list[-1:]
        
        return layer_output_list, last_state_list
    
    def _init_hidden(self, batch_size, image_size):
        """
        初始化所有层的隐藏状态
        """
        init_states = []
        for i in range(self.num_layers):
            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size))
        return init_states

class SSTPredictor(nn.Module):
    """
    SST预测模型
    """
    
    def __init__(self, input_channels, hidden_channels=[64, 32, 16], 
                 kernel_sizes=[3, 3, 3], output_channels=1, dropout=0.1):
        """
        初始化SST预测模型
        
        Args:
            input_channels: 输入通道数
            hidden_channels: ConvLSTM隐藏通道数列表
            kernel_sizes: 卷积核大小列表
            output_channels: 输出通道数
            dropout: Dropout比例
        """
        super(SSTPredictor, self).__init__()
        
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.num_layers = len(hidden_channels)
        
        # ConvLSTM主干网络
        self.convlstm = ConvLSTM(
            input_channels=input_channels,
            hidden_channels_list=hidden_channels,
            kernel_size_list=kernel_sizes,
            num_layers=self.num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 输出层
        self.output_conv = nn.Sequential(
            nn.Conv2d(hidden_channels[-1], hidden_channels[-1]//2, 3, padding=1),
            nn.BatchNorm2d(hidden_channels[-1]//2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_channels[-1]//2, output_channels, 1),
        )
        
        # 注意力机制（可选）
        self.attention = nn.Sequential(
            nn.Conv2d(hidden_channels[-1], hidden_channels[-1]//4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels[-1]//4, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch, seq_len, channels, height, width)
            
        Returns:
            output: 预测结果 (batch, output_channels, height, width)
        """
        # ConvLSTM处理
        layer_output_list, last_state_list = self.convlstm(x)
        
        # 取最后一个时间步的输出
        last_output = layer_output_list[0][:, -1, :, :, :]  # (batch, channels, height, width)
        
        # 注意力机制
        attention_weights = self.attention(last_output)
        attended_output = last_output * attention_weights
        
        # 输出预测
        output = self.output_conv(attended_output)
        
        return output

class MultiModalSSTPredictor(nn.Module):
    """
    多模态SST预测模型
    分别处理SST+IMF和ERA5数据，然后融合
    """
    
    def __init__(self, sst_channels, era5_channels, hidden_channels=[64, 32, 16], 
                 kernel_sizes=[3, 3, 3], fusion_channels=32, output_channels=1, dropout=0.1):
        """
        初始化多模态SST预测模型
        
        Args:
            sst_channels: SST+IMF通道数
            era5_channels: ERA5通道数
            hidden_channels: ConvLSTM隐藏通道数列表
            kernel_sizes: 卷积核大小列表
            fusion_channels: 融合层通道数
            output_channels: 输出通道数
            dropout: Dropout比例
        """
        super(MultiModalSSTPredictor, self).__init__()
        
        self.sst_channels = sst_channels
        self.era5_channels = era5_channels
        
        # SST+IMF分支
        self.sst_branch = ConvLSTM(
            input_channels=sst_channels,
            hidden_channels_list=hidden_channels,
            kernel_size_list=kernel_sizes,
            num_layers=len(hidden_channels),
            batch_first=True,
            return_all_layers=False
        )
        
        # ERA5分支
        self.era5_branch = ConvLSTM(
            input_channels=era5_channels,
            hidden_channels_list=[h//2 for h in hidden_channels],  # ERA5分支使用较少通道
            kernel_size_list=kernel_sizes,
            num_layers=len(hidden_channels),
            batch_first=True,
            return_all_layers=False
        )
        
        # 特征融合层
        total_features = hidden_channels[-1] + hidden_channels[-1]//2
        self.fusion = nn.Sequential(
            nn.Conv2d(total_features, fusion_channels, 3, padding=1),
            nn.BatchNorm2d(fusion_channels),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )
        
        # 输出层
        self.output_conv = nn.Sequential(
            nn.Conv2d(fusion_channels, fusion_channels//2, 3, padding=1),
            nn.BatchNorm2d(fusion_channels//2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(fusion_channels//2, output_channels, 1)
        )
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch, seq_len, total_channels, height, width)
            
        Returns:
            output: 预测结果 (batch, output_channels, height, width)
        """
        # 分离SST+IMF和ERA5数据
        sst_data = x[:, :, :self.sst_channels, :, :]
        era5_data = x[:, :, self.sst_channels:, :, :]
        
        # 分别处理两个分支
        sst_output, _ = self.sst_branch(sst_data)
        era5_output, _ = self.era5_branch(era5_data)
        
        # 取最后时间步的输出
        sst_features = sst_output[0][:, -1, :, :, :]
        era5_features = era5_output[0][:, -1, :, :, :]
        
        # 特征融合
        fused_features = torch.cat([sst_features, era5_features], dim=1)
        fused_features = self.fusion(fused_features)
        
        # 输出预测
        output = self.output_conv(fused_features)
        
        return output

def count_parameters(model):
    """
    计算模型参数数量
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def test_model():
    """
    测试模型
    """
    print("测试ConvLSTM模型...")
    
    # 模拟数据
    batch_size = 2
    seq_len = 14
    sst_channels = 7  # 1个原始SST + 6个IMF
    era5_channels = 4  # u10, v10, t2m, msl
    total_channels = sst_channels + era5_channels
    height, width = 100, 140
    
    # 创建模拟输入
    x = torch.randn(batch_size, seq_len, total_channels, height, width)
    
    # 创建模型
    model = MultiModalSSTPredictor(
        sst_channels=sst_channels,
        era5_channels=era5_channels,
        hidden_channels=[64, 32, 16],
        kernel_sizes=[3, 3, 3]
    )
    
    print(f"模型参数数量: {count_parameters(model):,}")
    
    # 前向传播
    with torch.no_grad():
        output = model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print("模型测试通过!")

if __name__ == "__main__":
    test_model()
